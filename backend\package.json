{"name": "order-management-backend", "version": "1.0.0", "description": "Order Management System Backend API", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["order", "management", "api", "express", "typescript"], "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^6.11.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "prisma": "^6.11.1"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^20.10.5", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}