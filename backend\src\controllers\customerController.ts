import { Request, Response } from 'express';
import prisma from '../lib/prisma';
import { ApiResponse, CreateCustomerRequest, UpdateCustomerRequest, PaginationParams } from '../types';
import { AppError, asyncHandler } from '../middleware/errorHandler';

// 获取所有客户（分页）
export const getCustomers = asyncHandler(async (req: Request, res: Response) => {
  const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc' } = req.query as any;
  
  const skip = (Number(page) - 1) * Number(limit);
  
  const [customers, total] = await Promise.all([
    prisma.customer.findMany({
      skip,
      take: Number(limit),
      orderBy: {
        [sortBy]: sortOrder,
      },
      include: {
        _count: {
          select: {
            salesOrders: true,
          },
        },
      },
    }),
    prisma.customer.count(),
  ]);

  const response: ApiResponse = {
    success: true,
    message: 'Customers retrieved successfully',
    data: {
      customers,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit)),
      },
    },
  };

  res.json(response);
});

// 根据ID获取单个客户
export const getCustomerById = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  const customer = await prisma.customer.findUnique({
    where: { id },
    include: {
      salesOrders: {
        orderBy: {
          createdAt: 'desc',
        },
        take: 10, // 最近10个订单
      },
      _count: {
        select: {
          salesOrders: true,
        },
      },
    },
  });

  if (!customer) {
    throw new AppError('Customer not found', 404);
  }

  const response: ApiResponse = {
    success: true,
    message: 'Customer retrieved successfully',
    data: customer,
  };

  res.json(response);
});

// 创建新客户
export const createCustomer = asyncHandler(async (req: Request, res: Response) => {
  const { customerName, contactPerson, address }: CreateCustomerRequest = req.body;

  const customer = await prisma.customer.create({
    data: {
      customerName,
      contactPerson,
      address,
    },
  });

  const response: ApiResponse = {
    success: true,
    message: 'Customer created successfully',
    data: customer,
  };

  res.status(201).json(response);
});

// 更新客户
export const updateCustomer = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const updateData: UpdateCustomerRequest = req.body;

  // 检查客户是否存在
  const existingCustomer = await prisma.customer.findUnique({
    where: { id },
  });

  if (!existingCustomer) {
    throw new AppError('Customer not found', 404);
  }

  const customer = await prisma.customer.update({
    where: { id },
    data: updateData,
  });

  const response: ApiResponse = {
    success: true,
    message: 'Customer updated successfully',
    data: customer,
  };

  res.json(response);
});

// 删除客户
export const deleteCustomer = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  // 检查客户是否存在
  const existingCustomer = await prisma.customer.findUnique({
    where: { id },
    include: {
      _count: {
        select: {
          salesOrders: true,
        },
      },
    },
  });

  if (!existingCustomer) {
    throw new AppError('Customer not found', 404);
  }

  // 检查是否有关联的订单
  if (existingCustomer._count.salesOrders > 0) {
    throw new AppError('Cannot delete customer with existing orders', 400);
  }

  await prisma.customer.delete({
    where: { id },
  });

  const response: ApiResponse = {
    success: true,
    message: 'Customer deleted successfully',
  };

  res.json(response);
});

// 搜索客户
export const searchCustomers = asyncHandler(async (req: Request, res: Response) => {
  const { q, page = 1, limit = 10 } = req.query as any;

  if (!q) {
    throw new AppError('Search query is required', 400);
  }

  const skip = (Number(page) - 1) * Number(limit);

  const [customers, total] = await Promise.all([
    prisma.customer.findMany({
      where: {
        OR: [
          {
            customerName: {
              contains: q,
              mode: 'insensitive',
            },
          },
          {
            contactPerson: {
              contains: q,
              mode: 'insensitive',
            },
          },
        ],
      },
      skip,
      take: Number(limit),
      orderBy: {
        customerName: 'asc',
      },
    }),
    prisma.customer.count({
      where: {
        OR: [
          {
            customerName: {
              contains: q,
              mode: 'insensitive',
            },
          },
          {
            contactPerson: {
              contains: q,
              mode: 'insensitive',
            },
          },
        ],
      },
    }),
  ]);

  const response: ApiResponse = {
    success: true,
    message: 'Customers search completed',
    data: {
      customers,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit)),
      },
    },
  };

  res.json(response);
});
