{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/utils/launch-editor.ts"], "sourcesContent": ["/**\n * MIT License\n *\n * Copyright (c) 2015-present, Facebook, Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\nimport { cyan, green, red } from '../../../../lib/picocolors'\nimport child_process from 'child_process'\nimport fs from 'fs'\nimport os from 'os'\nimport path from 'path'\nimport shellQuote from 'next/dist/compiled/shell-quote'\n\nfunction isTerminalEditor(editor: string) {\n  switch (editor) {\n    case 'vi':\n    case 'vim':\n    case 'nvim':\n    case 'emacs':\n    case 'nano': {\n      return true\n    }\n    default: {\n    }\n  }\n  return false\n}\n\n// Map from full process name to binary that starts the process\n// We can't just re-use full process name, because it will spawn a new instance\n// of the app every time\nconst COMMON_EDITORS_MACOS = {\n  '/Applications/Atom.app/Contents/MacOS/Atom': 'atom',\n  '/Applications/Atom Beta.app/Contents/MacOS/Atom Beta':\n    '/Applications/Atom Beta.app/Contents/MacOS/Atom Beta',\n  '/Applications/Brackets.app/Contents/MacOS/Brackets': 'brackets',\n  '/Applications/Sublime Text.app/Contents/MacOS/Sublime Text':\n    '/Applications/Sublime Text.app/Contents/SharedSupport/bin/subl',\n  '/Applications/Sublime Text Dev.app/Contents/MacOS/Sublime Text':\n    '/Applications/Sublime Text Dev.app/Contents/SharedSupport/bin/subl',\n  '/Applications/Sublime Text 2.app/Contents/MacOS/Sublime Text 2':\n    '/Applications/Sublime Text 2.app/Contents/SharedSupport/bin/subl',\n  '/Applications/Visual Studio Code.app/Contents/MacOS/Electron':\n    '/Applications/Visual Studio Code.app/Contents/Resources/app/bin/code',\n  '/Applications/Visual Studio Code - Insiders.app/Contents/MacOS/Electron':\n    '/Applications/Visual Studio Code - Insiders.app/Contents/Resources/app/bin/code',\n  '/Applications/VSCodium.app/Contents/MacOS/Electron':\n    '/Applications/VSCodium.app/Contents/Resources/app/bin/code',\n  '/Applications/AppCode.app/Contents/MacOS/appcode':\n    '/Applications/AppCode.app/Contents/MacOS/appcode',\n  '/Applications/CLion.app/Contents/MacOS/clion':\n    '/Applications/CLion.app/Contents/MacOS/clion',\n  '/Applications/IntelliJ IDEA.app/Contents/MacOS/idea':\n    '/Applications/IntelliJ IDEA.app/Contents/MacOS/idea',\n  '/Applications/PhpStorm.app/Contents/MacOS/phpstorm':\n    '/Applications/PhpStorm.app/Contents/MacOS/phpstorm',\n  '/Applications/PyCharm.app/Contents/MacOS/pycharm':\n    '/Applications/PyCharm.app/Contents/MacOS/pycharm',\n  '/Applications/PyCharm CE.app/Contents/MacOS/pycharm':\n    '/Applications/PyCharm CE.app/Contents/MacOS/pycharm',\n  '/Applications/RubyMine.app/Contents/MacOS/rubymine':\n    '/Applications/RubyMine.app/Contents/MacOS/rubymine',\n  '/Applications/WebStorm.app/Contents/MacOS/webstorm':\n    '/Applications/WebStorm.app/Contents/MacOS/webstorm',\n  '/Applications/MacVim.app/Contents/MacOS/MacVim': 'mvim',\n  '/Applications/GoLand.app/Contents/MacOS/goland':\n    '/Applications/GoLand.app/Contents/MacOS/goland',\n  '/Applications/Rider.app/Contents/MacOS/rider':\n    '/Applications/Rider.app/Contents/MacOS/rider',\n  '/Applications/Cursor.app/Contents/MacOS/Cursor':\n    '/Applications/Cursor.app/Contents/MacOS/Cursor',\n}\n\nconst COMMON_EDITORS_LINUX = {\n  atom: 'atom',\n  Brackets: 'brackets',\n  code: 'code',\n  'code-insiders': 'code-insiders',\n  vscodium: 'vscodium',\n  emacs: 'emacs',\n  gvim: 'gvim',\n  'idea.sh': 'idea',\n  'phpstorm.sh': 'phpstorm',\n  'pycharm.sh': 'pycharm',\n  'rubymine.sh': 'rubymine',\n  sublime_text: 'sublime_text',\n  vim: 'vim',\n  nvim: 'nvim',\n  'webstorm.sh': 'webstorm',\n  'goland.sh': 'goland',\n  'rider.sh': 'rider',\n}\n\nconst COMMON_EDITORS_WIN = [\n  'Brackets.exe',\n  'Code.exe',\n  'Code - Insiders.exe',\n  'VSCodium.exe',\n  'atom.exe',\n  'sublime_text.exe',\n  'notepad++.exe',\n  'clion.exe',\n  'clion64.exe',\n  'idea.exe',\n  'idea64.exe',\n  'phpstorm.exe',\n  'phpstorm64.exe',\n  'pycharm.exe',\n  'pycharm64.exe',\n  'rubymine.exe',\n  'rubymine64.exe',\n  'webstorm.exe',\n  'webstorm64.exe',\n  'goland.exe',\n  'goland64.exe',\n  'rider.exe',\n  'rider64.exe',\n]\n\n// Transpiled version of: /^([A-Za-z]:[/\\\\])?[\\p{L}0-9/.\\-_\\\\]+$/u\n// Non-transpiled version requires support for Unicode property regex. Allows\n// alphanumeric characters, periods, dashes, slashes, and underscores.\nconst WINDOWS_FILE_NAME_ACCESS_LIST =\n  /^([A-Za-z]:[/\\\\])?(?:[\\x2D-9A-Z\\\\_a-z\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0370-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0560-\\u0588\\u05D0-\\u05EA\\u05EF-\\u05F2\\u0620-\\u064A\\u066E\\u066F\\u0671-\\u06D3\\u06D5\\u06E5\\u06E6\\u06EE\\u06EF\\u06FA-\\u06FC\\u06FF\\u0710\\u0712-\\u072F\\u074D-\\u07A5\\u07B1\\u07CA-\\u07EA\\u07F4\\u07F5\\u07FA\\u0800-\\u0815\\u081A\\u0824\\u0828\\u0840-\\u0858\\u0860-\\u086A\\u08A0-\\u08B4\\u08B6-\\u08BD\\u0904-\\u0939\\u093D\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BD\\u09CE\\u09DC\\u09DD\\u09DF-\\u09E1\\u09F0\\u09F1\\u09FC\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A59-\\u0A5C\\u0A5E\\u0A72-\\u0A74\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABD\\u0AD0\\u0AE0\\u0AE1\\u0AF9\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3D\\u0B5C\\u0B5D\\u0B5F-\\u0B61\\u0B71\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BD0\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D\\u0C58-\\u0C5A\\u0C60\\u0C61\\u0C80\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBD\\u0CDE\\u0CE0\\u0CE1\\u0CF1\\u0CF2\\u0D05-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D\\u0D4E\\u0D54-\\u0D56\\u0D5F-\\u0D61\\u0D7A-\\u0D7F\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0E01-\\u0E30\\u0E32\\u0E33\\u0E40-\\u0E46\\u0E81\\u0E82\\u0E84\\u0E87\\u0E88\\u0E8A\\u0E8D\\u0E94-\\u0E97\\u0E99-\\u0E9F\\u0EA1-\\u0EA3\\u0EA5\\u0EA7\\u0EAA\\u0EAB\\u0EAD-\\u0EB0\\u0EB2\\u0EB3\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EDC-\\u0EDF\\u0F00\\u0F40-\\u0F47\\u0F49-\\u0F6C\\u0F88-\\u0F8C\\u1000-\\u102A\\u103F\\u1050-\\u1055\\u105A-\\u105D\\u1061\\u1065\\u1066\\u106E-\\u1070\\u1075-\\u1081\\u108E\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u1380-\\u138F\\u13A0-\\u13F5\\u13F8-\\u13FD\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16F1-\\u16F8\\u1700-\\u170C\\u170E-\\u1711\\u1720-\\u1731\\u1740-\\u1751\\u1760-\\u176C\\u176E-\\u1770\\u1780-\\u17B3\\u17D7\\u17DC\\u1820-\\u1878\\u1880-\\u1884\\u1887-\\u18A8\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1950-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u1A00-\\u1A16\\u1A20-\\u1A54\\u1AA7\\u1B05-\\u1B33\\u1B45-\\u1B4B\\u1B83-\\u1BA0\\u1BAE\\u1BAF\\u1BBA-\\u1BE5\\u1C00-\\u1C23\\u1C4D-\\u1C4F\\u1C5A-\\u1C7D\\u1C80-\\u1C88\\u1C90-\\u1CBA\\u1CBD-\\u1CBF\\u1CE9-\\u1CEC\\u1CEE-\\u1CF1\\u1CF5\\u1CF6\\u1D00-\\u1DBF\\u1E00-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2183\\u2184\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005\\u3006\\u3031-\\u3035\\u303B\\u303C\\u3041-\\u3096\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312F\\u3131-\\u318E\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3400-\\u4DB5\\u4E00-\\u9FEF\\uA000-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA61F\\uA62A\\uA62B\\uA640-\\uA66E\\uA67F-\\uA69D\\uA6A0-\\uA6E5\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA7B9\\uA7F7-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8F2-\\uA8F7\\uA8FB\\uA8FD\\uA8FE\\uA90A-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF\\uA9E0-\\uA9E4\\uA9E6-\\uA9EF\\uA9FA-\\uA9FE\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA60-\\uAA76\\uAA7A\\uAA7E-\\uAAAF\\uAAB1\\uAAB5\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB65\\uAB70-\\uABE2\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]|\\uD800[\\uDC00-\\uDC0B\\uDC0D-\\uDC26\\uDC28-\\uDC3A\\uDC3C\\uDC3D\\uDC3F-\\uDC4D\\uDC50-\\uDC5D\\uDC80-\\uDCFA\\uDE80-\\uDE9C\\uDEA0-\\uDED0\\uDF00-\\uDF1F\\uDF2D-\\uDF40\\uDF42-\\uDF49\\uDF50-\\uDF75\\uDF80-\\uDF9D\\uDFA0-\\uDFC3\\uDFC8-\\uDFCF]|\\uD801[\\uDC00-\\uDC9D\\uDCB0-\\uDCD3\\uDCD8-\\uDCFB\\uDD00-\\uDD27\\uDD30-\\uDD63\\uDE00-\\uDF36\\uDF40-\\uDF55\\uDF60-\\uDF67]|\\uD802[\\uDC00-\\uDC05\\uDC08\\uDC0A-\\uDC35\\uDC37\\uDC38\\uDC3C\\uDC3F-\\uDC55\\uDC60-\\uDC76\\uDC80-\\uDC9E\\uDCE0-\\uDCF2\\uDCF4\\uDCF5\\uDD00-\\uDD15\\uDD20-\\uDD39\\uDD80-\\uDDB7\\uDDBE\\uDDBF\\uDE00\\uDE10-\\uDE13\\uDE15-\\uDE17\\uDE19-\\uDE35\\uDE60-\\uDE7C\\uDE80-\\uDE9C\\uDEC0-\\uDEC7\\uDEC9-\\uDEE4\\uDF00-\\uDF35\\uDF40-\\uDF55\\uDF60-\\uDF72\\uDF80-\\uDF91]|\\uD803[\\uDC00-\\uDC48\\uDC80-\\uDCB2\\uDCC0-\\uDCF2\\uDD00-\\uDD23\\uDF00-\\uDF1C\\uDF27\\uDF30-\\uDF45]|\\uD804[\\uDC03-\\uDC37\\uDC83-\\uDCAF\\uDCD0-\\uDCE8\\uDD03-\\uDD26\\uDD44\\uDD50-\\uDD72\\uDD76\\uDD83-\\uDDB2\\uDDC1-\\uDDC4\\uDDDA\\uDDDC\\uDE00-\\uDE11\\uDE13-\\uDE2B\\uDE80-\\uDE86\\uDE88\\uDE8A-\\uDE8D\\uDE8F-\\uDE9D\\uDE9F-\\uDEA8\\uDEB0-\\uDEDE\\uDF05-\\uDF0C\\uDF0F\\uDF10\\uDF13-\\uDF28\\uDF2A-\\uDF30\\uDF32\\uDF33\\uDF35-\\uDF39\\uDF3D\\uDF50\\uDF5D-\\uDF61]|\\uD805[\\uDC00-\\uDC34\\uDC47-\\uDC4A\\uDC80-\\uDCAF\\uDCC4\\uDCC5\\uDCC7\\uDD80-\\uDDAE\\uDDD8-\\uDDDB\\uDE00-\\uDE2F\\uDE44\\uDE80-\\uDEAA\\uDF00-\\uDF1A]|\\uD806[\\uDC00-\\uDC2B\\uDCA0-\\uDCDF\\uDCFF\\uDE00\\uDE0B-\\uDE32\\uDE3A\\uDE50\\uDE5C-\\uDE83\\uDE86-\\uDE89\\uDE9D\\uDEC0-\\uDEF8]|\\uD807[\\uDC00-\\uDC08\\uDC0A-\\uDC2E\\uDC40\\uDC72-\\uDC8F\\uDD00-\\uDD06\\uDD08\\uDD09\\uDD0B-\\uDD30\\uDD46\\uDD60-\\uDD65\\uDD67\\uDD68\\uDD6A-\\uDD89\\uDD98\\uDEE0-\\uDEF2]|\\uD808[\\uDC00-\\uDF99]|\\uD809[\\uDC80-\\uDD43]|[\\uD80C\\uD81C-\\uD820\\uD840-\\uD868\\uD86A-\\uD86C\\uD86F-\\uD872\\uD874-\\uD879][\\uDC00-\\uDFFF]|\\uD80D[\\uDC00-\\uDC2E]|\\uD811[\\uDC00-\\uDE46]|\\uD81A[\\uDC00-\\uDE38\\uDE40-\\uDE5E\\uDED0-\\uDEED\\uDF00-\\uDF2F\\uDF40-\\uDF43\\uDF63-\\uDF77\\uDF7D-\\uDF8F]|\\uD81B[\\uDE40-\\uDE7F\\uDF00-\\uDF44\\uDF50\\uDF93-\\uDF9F\\uDFE0\\uDFE1]|\\uD821[\\uDC00-\\uDFF1]|\\uD822[\\uDC00-\\uDEF2]|\\uD82C[\\uDC00-\\uDD1E\\uDD70-\\uDEFB]|\\uD82F[\\uDC00-\\uDC6A\\uDC70-\\uDC7C\\uDC80-\\uDC88\\uDC90-\\uDC99]|\\uD835[\\uDC00-\\uDC54\\uDC56-\\uDC9C\\uDC9E\\uDC9F\\uDCA2\\uDCA5\\uDCA6\\uDCA9-\\uDCAC\\uDCAE-\\uDCB9\\uDCBB\\uDCBD-\\uDCC3\\uDCC5-\\uDD05\\uDD07-\\uDD0A\\uDD0D-\\uDD14\\uDD16-\\uDD1C\\uDD1E-\\uDD39\\uDD3B-\\uDD3E\\uDD40-\\uDD44\\uDD46\\uDD4A-\\uDD50\\uDD52-\\uDEA5\\uDEA8-\\uDEC0\\uDEC2-\\uDEDA\\uDEDC-\\uDEFA\\uDEFC-\\uDF14\\uDF16-\\uDF34\\uDF36-\\uDF4E\\uDF50-\\uDF6E\\uDF70-\\uDF88\\uDF8A-\\uDFA8\\uDFAA-\\uDFC2\\uDFC4-\\uDFCB]|\\uD83A[\\uDC00-\\uDCC4\\uDD00-\\uDD43]|\\uD83B[\\uDE00-\\uDE03\\uDE05-\\uDE1F\\uDE21\\uDE22\\uDE24\\uDE27\\uDE29-\\uDE32\\uDE34-\\uDE37\\uDE39\\uDE3B\\uDE42\\uDE47\\uDE49\\uDE4B\\uDE4D-\\uDE4F\\uDE51\\uDE52\\uDE54\\uDE57\\uDE59\\uDE5B\\uDE5D\\uDE5F\\uDE61\\uDE62\\uDE64\\uDE67-\\uDE6A\\uDE6C-\\uDE72\\uDE74-\\uDE77\\uDE79-\\uDE7C\\uDE7E\\uDE80-\\uDE89\\uDE8B-\\uDE9B\\uDEA1-\\uDEA3\\uDEA5-\\uDEA9\\uDEAB-\\uDEBB]|\\uD869[\\uDC00-\\uDED6\\uDF00-\\uDFFF]|\\uD86D[\\uDC00-\\uDF34\\uDF40-\\uDFFF]|\\uD86E[\\uDC00-\\uDC1D\\uDC20-\\uDFFF]|\\uD873[\\uDC00-\\uDEA1\\uDEB0-\\uDFFF]|\\uD87A[\\uDC00-\\uDFE0]|\\uD87E[\\uDC00-\\uDE1D])+$/\n\nfunction getArgumentsForLineNumber(\n  editor: string,\n  fileName: string,\n  lineNumber: number,\n  colNumber: number\n): string[] {\n  const editorBasename = path.basename(editor).replace(/\\.(exe|cmd|bat)$/i, '')\n  switch (editorBasename) {\n    case 'atom':\n    case 'Atom':\n    case 'Atom Beta':\n    case 'subl':\n    case 'sublime':\n    case 'sublime_text': {\n      return [fileName + ':' + lineNumber + ':' + colNumber]\n    }\n    case 'wstorm':\n    case 'charm': {\n      return [fileName + ':' + lineNumber]\n    }\n    case 'notepad++': {\n      return ['-n' + lineNumber, '-c' + colNumber, fileName]\n    }\n    case 'vim':\n    case 'nvim':\n    case 'mvim':\n    case 'joe':\n    case 'gvim': {\n      return ['+' + lineNumber, fileName]\n    }\n    case 'emacs':\n    case 'emacsclient': {\n      return ['+' + lineNumber + ':' + colNumber, fileName]\n    }\n    case 'rmate':\n    case 'mate':\n    case 'mine': {\n      return ['--line', lineNumber.toString(), fileName]\n    }\n    case 'code':\n    case 'Code':\n    case 'Cursor':\n    case 'code-insiders':\n    case 'Code - Insiders':\n    case 'vscodium':\n    case 'VSCodium': {\n      return ['-g', fileName + ':' + lineNumber + ':' + colNumber]\n    }\n    case 'appcode':\n    case 'clion':\n    case 'clion64':\n    case 'idea':\n    case 'idea64':\n    case 'phpstorm':\n    case 'phpstorm64':\n    case 'pycharm':\n    case 'pycharm64':\n    case 'rubymine':\n    case 'rubymine64':\n    case 'webstorm':\n    case 'webstorm64':\n    case 'goland':\n    case 'goland64':\n    case 'rider':\n    case 'rider64': {\n      return ['--line', lineNumber.toString(), fileName]\n    }\n    default: {\n      // For all others, drop the lineNumber until we have\n      // a mapping above, since providing the lineNumber incorrectly\n      // can result in errors or confusing behavior.\n      return [fileName]\n    }\n  }\n}\n\nfunction guessEditor(): string[] {\n  // Explicit config always wins\n  if (process.env.REACT_EDITOR) {\n    return shellQuote.parse(process.env.REACT_EDITOR) as any\n  }\n\n  // We can find out which editor is currently running by:\n  // `ps x` on macOS and Linux\n  // `Get-Process` on Windows\n  try {\n    if (process.platform === 'darwin') {\n      const output = child_process.execSync('ps x').toString()\n      const processNames = Object.keys(COMMON_EDITORS_MACOS)\n      for (let i = 0; i < processNames.length; i++) {\n        const processName = processNames[i]\n        if (output.indexOf(processName) !== -1) {\n          return [(COMMON_EDITORS_MACOS as any)[processName]]\n        }\n      }\n    } else if (process.platform === 'win32') {\n      // Some processes need elevated rights to get its executable path.\n      // Just filter them out upfront. This also saves 10-20ms on the command.\n      const output = child_process\n        .execSync(\n          'wmic process where \"executablepath is not null\" get executablepath'\n        )\n        .toString()\n      const runningProcesses = output.split('\\r\\n')\n      for (let i = 0; i < runningProcesses.length; i++) {\n        const processPath = runningProcesses[i].trim()\n        const processName = path.basename(processPath)\n        if (COMMON_EDITORS_WIN.indexOf(processName) !== -1) {\n          return [processPath]\n        }\n      }\n    } else if (process.platform === 'linux') {\n      // --no-heading No header line\n      // x List all processes owned by you\n      // -o comm Need only names column\n      const output = child_process\n        .execSync('ps x --no-heading -o comm --sort=comm')\n        .toString()\n      const processNames = Object.keys(COMMON_EDITORS_LINUX)\n      for (let i = 0; i < processNames.length; i++) {\n        const processName = processNames[i]\n        if (output.indexOf(processName) !== -1) {\n          return [(COMMON_EDITORS_LINUX as any)[processName] as string]\n        }\n      }\n    }\n  } catch (error) {\n    // Ignore...\n  }\n\n  // Last resort, use old skool env vars\n  if (process.env.VISUAL) {\n    return [process.env.VISUAL]\n  } else if (process.env.EDITOR) {\n    return [process.env.EDITOR]\n  }\n\n  return []\n}\n\nfunction printInstructions(fileName: string, errorMessage: string | null) {\n  console.log()\n  console.log(\n    red('Could not open ' + path.basename(fileName) + ' in the editor.')\n  )\n  if (errorMessage) {\n    if (errorMessage[errorMessage.length - 1] !== '.') {\n      errorMessage += '.'\n    }\n    console.log(red('The editor process exited with an error: ' + errorMessage))\n  }\n  console.log()\n  console.log(\n    'To set up the editor integration, add something like ' +\n      cyan('REACT_EDITOR=atom') +\n      ' to the ' +\n      green('.env.local') +\n      ' file in your project folder ' +\n      'and restart the development server.'\n  )\n  console.log()\n}\n\nexport function escapeApplescriptStringFragment(input: string): string {\n  // The only two special characters in a quoted applescript string are\n  // backslash and double quote. Both are escaped with a preceeding backslash.\n  //\n  // Some whitespace characters (like newlines) can be escaped (as `\\n`), but\n  // aren't required to be escaped (so we're not bothering to do that).\n  //\n  // https://developer.apple.com/library/archive/documentation/AppleScript/Conceptual/AppleScriptLangGuide/reference/ASLR_classes.html#//apple_ref/doc/uid/TP40000983-CH1g-BBCIAHJF:~:text=Special%20String%20Characters\n  return input.replaceAll(/[\\\\\"]/g, (original) => `\\\\${original}`)\n}\n\nexport function launchEditor(\n  fileName: string,\n  lineNumber: number,\n  colNumber: number\n) {\n  if (!fs.existsSync(fileName)) {\n    return\n  }\n\n  // Sanitize lineNumber to prevent malicious use on win32\n  // via: https://github.com/nodejs/node/blob/c3bb4b1aa5e907d489619fb43d233c3336bfc03d/lib/child_process.js#L333\n  // and it should be a positive integer\n  if (!(Number.isInteger(lineNumber) && lineNumber > 0)) {\n    return\n  }\n\n  // colNumber is optional, but should be a positive integer too\n  // default is 1\n  if (!(Number.isInteger(colNumber) && colNumber > 0)) {\n    colNumber = 1\n  }\n\n  let [editor, ...args] = guessEditor()\n\n  if (!editor) {\n    printInstructions(fileName, null)\n    return\n  }\n\n  if (editor.toLowerCase() === 'none') {\n    return\n  }\n\n  if (\n    process.platform === 'linux' &&\n    fileName.startsWith('/mnt/') &&\n    /Microsoft/i.test(os.release())\n  ) {\n    // Assume WSL / \"Bash on Ubuntu on Windows\" is being used, and\n    // that the file exists on the Windows file system.\n    // `os.release()` is \"4.4.0-43-Microsoft\" in the current release\n    // build of WSL, see: https://github.com/Microsoft/BashOnWindows/issues/423#issuecomment-221627364\n    // When a Windows editor is specified, interop functionality can\n    // handle the path translation, but only if a relative path is used.\n    fileName = path.relative('', fileName)\n  }\n\n  // cmd.exe on Windows is vulnerable to RCE attacks given a file name of the\n  // form \"C:\\Users\\<USER>\\Downloads\\& curl 172.21.93.52\". Use an access list\n  // to validate user-provided file names. This doesn't cover the entire range\n  // of valid file names but should cover almost all of them in practice.\n  if (\n    process.platform === 'win32' &&\n    !WINDOWS_FILE_NAME_ACCESS_LIST.test(fileName.trim())\n  ) {\n    console.log()\n    console.log(\n      red('Could not open ' + path.basename(fileName) + ' in the editor.')\n    )\n    console.log()\n    console.log(\n      'When running on Windows, file names are checked against an access list ' +\n        'to protect against remote code execution attacks. File names may ' +\n        'consist only of alphanumeric characters (all languages), periods, ' +\n        'dashes, slashes, and underscores.'\n    )\n    console.log()\n    return\n  }\n\n  if (lineNumber) {\n    args = args.concat(\n      getArgumentsForLineNumber(editor, fileName, lineNumber, colNumber)\n    )\n  } else {\n    args.push(fileName)\n  }\n\n  let p: child_process.ChildProcess | undefined = undefined\n  if (process.platform === 'win32') {\n    // On Windows, launch the editor in a shell because spawn can only\n    // launch .exe files.\n    p = child_process.spawn('cmd.exe', ['/C', editor].concat(args), {\n      stdio: 'inherit',\n      detached: true,\n    })\n  } else if (isTerminalEditor(editor)) {\n    if (process.platform === 'darwin') {\n      const escapedScript = escapeApplescriptStringFragment(\n        shellQuote.quote([editor, ...args])\n      )\n      p = child_process.spawn(\n        'osascript',\n        ['-e', `tell application \"Terminal\" to do script \"${escapedScript}\"`],\n        { stdio: 'ignore' }\n      )\n    } else {\n      printInstructions(fileName, 'Terminal editors can only be used on macOS.')\n    }\n  } else {\n    p = child_process.spawn(editor, args, { stdio: 'inherit' })\n  }\n\n  if (p) {\n    p.on('exit', function (errorCode) {\n      if (errorCode) {\n        printInstructions(fileName, '(code ' + errorCode + ')')\n      }\n    })\n    p.on('error', function (error) {\n      printInstructions(fileName, error.message)\n    })\n  }\n}\n"], "names": ["escapeApplescriptStringFragment", "launchEditor", "isTerminalEditor", "editor", "COMMON_EDITORS_MACOS", "COMMON_EDITORS_LINUX", "atom", "Brackets", "code", "vscodium", "emacs", "gvim", "sublime_text", "vim", "nvim", "COMMON_EDITORS_WIN", "WINDOWS_FILE_NAME_ACCESS_LIST", "getArgumentsForLineNumber", "fileName", "lineNumber", "colNumber", "editorBasename", "path", "basename", "replace", "toString", "guessEditor", "process", "env", "REACT_EDITOR", "shellQuote", "parse", "platform", "output", "child_process", "execSync", "processNames", "Object", "keys", "i", "length", "processName", "indexOf", "runningProcesses", "split", "processPath", "trim", "error", "VISUAL", "EDITOR", "printInstructions", "errorMessage", "console", "log", "red", "cyan", "green", "input", "replaceAll", "original", "fs", "existsSync", "Number", "isInteger", "args", "toLowerCase", "startsWith", "test", "os", "release", "relative", "concat", "push", "p", "undefined", "spawn", "stdio", "detached", "escapedScript", "quote", "on", "errorCode", "message"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;CAsBC;;;;;;;;;;;;;;;IA2ReA,+BAA+B;eAA/BA;;IAWAC,YAAY;eAAZA;;;;4BArSiB;wEACP;6DACX;6DACA;+DACE;qEACM;AAEvB,SAASC,iBAAiBC,MAAc;IACtC,OAAQA;QACN,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAQ;gBACX,OAAO;YACT;QACA;YAAS,CACT;IACF;IACA,OAAO;AACT;AAEA,+DAA+D;AAC/D,+EAA+E;AAC/E,wBAAwB;AACxB,MAAMC,uBAAuB;IAC3B,8CAA8C;IAC9C,wDACE;IACF,sDAAsD;IACtD,8DACE;IACF,kEACE;IACF,kEACE;IACF,gEACE;IACF,2EACE;IACF,sDACE;IACF,oDACE;IACF,gDACE;IACF,uDACE;IACF,sDACE;IACF,oDACE;IACF,uDACE;IACF,sDACE;IACF,sDACE;IACF,kDAAkD;IAClD,kDACE;IACF,gDACE;IACF,kDACE;AACJ;AAEA,MAAMC,uBAAuB;IAC3BC,MAAM;IACNC,UAAU;IACVC,MAAM;IACN,iBAAiB;IACjBC,UAAU;IACVC,OAAO;IACPC,MAAM;IACN,WAAW;IACX,eAAe;IACf,cAAc;IACd,eAAe;IACfC,cAAc;IACdC,KAAK;IACLC,MAAM;IACN,eAAe;IACf,aAAa;IACb,YAAY;AACd;AAEA,MAAMC,qBAAqB;IACzB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,kEAAkE;AAClE,6EAA6E;AAC7E,sEAAsE;AACtE,MAAMC,gCACJ;AAEF,SAASC,0BACPd,MAAc,EACde,QAAgB,EAChBC,UAAkB,EAClBC,SAAiB;IAEjB,MAAMC,iBAAiBC,aAAI,CAACC,QAAQ,CAACpB,QAAQqB,OAAO,CAAC,qBAAqB;IAC1E,OAAQH;QACN,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAgB;gBACnB,OAAO;oBAACH,WAAW,MAAMC,aAAa,MAAMC;iBAAU;YACxD;QACA,KAAK;QACL,KAAK;YAAS;gBACZ,OAAO;oBAACF,WAAW,MAAMC;iBAAW;YACtC;QACA,KAAK;YAAa;gBAChB,OAAO;oBAAC,OAAOA;oBAAY,OAAOC;oBAAWF;iBAAS;YACxD;QACA,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAQ;gBACX,OAAO;oBAAC,MAAMC;oBAAYD;iBAAS;YACrC;QACA,KAAK;QACL,KAAK;YAAe;gBAClB,OAAO;oBAAC,MAAMC,aAAa,MAAMC;oBAAWF;iBAAS;YACvD;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAQ;gBACX,OAAO;oBAAC;oBAAUC,WAAWM,QAAQ;oBAAIP;iBAAS;YACpD;QACA,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAY;gBACf,OAAO;oBAAC;oBAAMA,WAAW,MAAMC,aAAa,MAAMC;iBAAU;YAC9D;QACA,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAW;gBACd,OAAO;oBAAC;oBAAUD,WAAWM,QAAQ;oBAAIP;iBAAS;YACpD;QACA;YAAS;gBACP,oDAAoD;gBACpD,8DAA8D;gBAC9D,8CAA8C;gBAC9C,OAAO;oBAACA;iBAAS;YACnB;IACF;AACF;AAEA,SAASQ;IACP,8BAA8B;IAC9B,IAAIC,QAAQC,GAAG,CAACC,YAAY,EAAE;QAC5B,OAAOC,mBAAU,CAACC,KAAK,CAACJ,QAAQC,GAAG,CAACC,YAAY;IAClD;IAEA,wDAAwD;IACxD,4BAA4B;IAC5B,2BAA2B;IAC3B,IAAI;QACF,IAAIF,QAAQK,QAAQ,KAAK,UAAU;YACjC,MAAMC,SAASC,sBAAa,CAACC,QAAQ,CAAC,QAAQV,QAAQ;YACtD,MAAMW,eAAeC,OAAOC,IAAI,CAAClC;YACjC,IAAK,IAAImC,IAAI,GAAGA,IAAIH,aAAaI,MAAM,EAAED,IAAK;gBAC5C,MAAME,cAAcL,YAAY,CAACG,EAAE;gBACnC,IAAIN,OAAOS,OAAO,CAACD,iBAAiB,CAAC,GAAG;oBACtC,OAAO;wBAAErC,oBAA4B,CAACqC,YAAY;qBAAC;gBACrD;YACF;QACF,OAAO,IAAId,QAAQK,QAAQ,KAAK,SAAS;YACvC,kEAAkE;YAClE,wEAAwE;YACxE,MAAMC,SAASC,sBAAa,CACzBC,QAAQ,CACP,sEAEDV,QAAQ;YACX,MAAMkB,mBAAmBV,OAAOW,KAAK,CAAC;YACtC,IAAK,IAAIL,IAAI,GAAGA,IAAII,iBAAiBH,MAAM,EAAED,IAAK;gBAChD,MAAMM,cAAcF,gBAAgB,CAACJ,EAAE,CAACO,IAAI;gBAC5C,MAAML,cAAcnB,aAAI,CAACC,QAAQ,CAACsB;gBAClC,IAAI9B,mBAAmB2B,OAAO,CAACD,iBAAiB,CAAC,GAAG;oBAClD,OAAO;wBAACI;qBAAY;gBACtB;YACF;QACF,OAAO,IAAIlB,QAAQK,QAAQ,KAAK,SAAS;YACvC,8BAA8B;YAC9B,oCAAoC;YACpC,iCAAiC;YACjC,MAAMC,SAASC,sBAAa,CACzBC,QAAQ,CAAC,yCACTV,QAAQ;YACX,MAAMW,eAAeC,OAAOC,IAAI,CAACjC;YACjC,IAAK,IAAIkC,IAAI,GAAGA,IAAIH,aAAaI,MAAM,EAAED,IAAK;gBAC5C,MAAME,cAAcL,YAAY,CAACG,EAAE;gBACnC,IAAIN,OAAOS,OAAO,CAACD,iBAAiB,CAAC,GAAG;oBACtC,OAAO;wBAAEpC,oBAA4B,CAACoC,YAAY;qBAAW;gBAC/D;YACF;QACF;IACF,EAAE,OAAOM,OAAO;IACd,YAAY;IACd;IAEA,sCAAsC;IACtC,IAAIpB,QAAQC,GAAG,CAACoB,MAAM,EAAE;QACtB,OAAO;YAACrB,QAAQC,GAAG,CAACoB,MAAM;SAAC;IAC7B,OAAO,IAAIrB,QAAQC,GAAG,CAACqB,MAAM,EAAE;QAC7B,OAAO;YAACtB,QAAQC,GAAG,CAACqB,MAAM;SAAC;IAC7B;IAEA,OAAO,EAAE;AACX;AAEA,SAASC,kBAAkBhC,QAAgB,EAAEiC,YAA2B;IACtEC,QAAQC,GAAG;IACXD,QAAQC,GAAG,CACTC,IAAAA,eAAG,EAAC,oBAAoBhC,aAAI,CAACC,QAAQ,CAACL,YAAY;IAEpD,IAAIiC,cAAc;QAChB,IAAIA,YAAY,CAACA,aAAaX,MAAM,GAAG,EAAE,KAAK,KAAK;YACjDW,gBAAgB;QAClB;QACAC,QAAQC,GAAG,CAACC,IAAAA,eAAG,EAAC,8CAA8CH;IAChE;IACAC,QAAQC,GAAG;IACXD,QAAQC,GAAG,CACT,0DACEE,IAAAA,gBAAI,EAAC,uBACL,aACAC,IAAAA,iBAAK,EAAC,gBACN,kCACA;IAEJJ,QAAQC,GAAG;AACb;AAEO,SAASrD,gCAAgCyD,KAAa;IAC3D,qEAAqE;IACrE,4EAA4E;IAC5E,EAAE;IACF,2EAA2E;IAC3E,qEAAqE;IACrE,EAAE;IACF,sNAAsN;IACtN,OAAOA,MAAMC,UAAU,CAAC,UAAU,CAACC,WAAa,AAAC,OAAIA;AACvD;AAEO,SAAS1D,aACdiB,QAAgB,EAChBC,UAAkB,EAClBC,SAAiB;IAEjB,IAAI,CAACwC,WAAE,CAACC,UAAU,CAAC3C,WAAW;QAC5B;IACF;IAEA,wDAAwD;IACxD,8GAA8G;IAC9G,sCAAsC;IACtC,IAAI,CAAE4C,CAAAA,OAAOC,SAAS,CAAC5C,eAAeA,aAAa,CAAA,GAAI;QACrD;IACF;IAEA,8DAA8D;IAC9D,eAAe;IACf,IAAI,CAAE2C,CAAAA,OAAOC,SAAS,CAAC3C,cAAcA,YAAY,CAAA,GAAI;QACnDA,YAAY;IACd;IAEA,IAAI,CAACjB,QAAQ,GAAG6D,KAAK,GAAGtC;IAExB,IAAI,CAACvB,QAAQ;QACX+C,kBAAkBhC,UAAU;QAC5B;IACF;IAEA,IAAIf,OAAO8D,WAAW,OAAO,QAAQ;QACnC;IACF;IAEA,IACEtC,QAAQK,QAAQ,KAAK,WACrBd,SAASgD,UAAU,CAAC,YACpB,aAAaC,IAAI,CAACC,WAAE,CAACC,OAAO,KAC5B;QACA,8DAA8D;QAC9D,mDAAmD;QACnD,gEAAgE;QAChE,kGAAkG;QAClG,gEAAgE;QAChE,oEAAoE;QACpEnD,WAAWI,aAAI,CAACgD,QAAQ,CAAC,IAAIpD;IAC/B;IAEA,2EAA2E;IAC3E,+EAA+E;IAC/E,4EAA4E;IAC5E,uEAAuE;IACvE,IACES,QAAQK,QAAQ,KAAK,WACrB,CAAChB,8BAA8BmD,IAAI,CAACjD,SAAS4B,IAAI,KACjD;QACAM,QAAQC,GAAG;QACXD,QAAQC,GAAG,CACTC,IAAAA,eAAG,EAAC,oBAAoBhC,aAAI,CAACC,QAAQ,CAACL,YAAY;QAEpDkC,QAAQC,GAAG;QACXD,QAAQC,GAAG,CACT,4EACE,sEACA,uEACA;QAEJD,QAAQC,GAAG;QACX;IACF;IAEA,IAAIlC,YAAY;QACd6C,OAAOA,KAAKO,MAAM,CAChBtD,0BAA0Bd,QAAQe,UAAUC,YAAYC;IAE5D,OAAO;QACL4C,KAAKQ,IAAI,CAACtD;IACZ;IAEA,IAAIuD,IAA4CC;IAChD,IAAI/C,QAAQK,QAAQ,KAAK,SAAS;QAChC,kEAAkE;QAClE,qBAAqB;QACrByC,IAAIvC,sBAAa,CAACyC,KAAK,CAAC,WAAW;YAAC;YAAMxE;SAAO,CAACoE,MAAM,CAACP,OAAO;YAC9DY,OAAO;YACPC,UAAU;QACZ;IACF,OAAO,IAAI3E,iBAAiBC,SAAS;QACnC,IAAIwB,QAAQK,QAAQ,KAAK,UAAU;YACjC,MAAM8C,gBAAgB9E,gCACpB8B,mBAAU,CAACiD,KAAK,CAAC;gBAAC5E;mBAAW6D;aAAK;YAEpCS,IAAIvC,sBAAa,CAACyC,KAAK,CACrB,aACA;gBAAC;gBAAO,+CAA4CG,gBAAc;aAAG,EACrE;gBAAEF,OAAO;YAAS;QAEtB,OAAO;YACL1B,kBAAkBhC,UAAU;QAC9B;IACF,OAAO;QACLuD,IAAIvC,sBAAa,CAACyC,KAAK,CAACxE,QAAQ6D,MAAM;YAAEY,OAAO;QAAU;IAC3D;IAEA,IAAIH,GAAG;QACLA,EAAEO,EAAE,CAAC,QAAQ,SAAUC,SAAS;YAC9B,IAAIA,WAAW;gBACb/B,kBAAkBhC,UAAU,WAAW+D,YAAY;YACrD;QACF;QACAR,EAAEO,EAAE,CAAC,SAAS,SAAUjC,KAAK;YAC3BG,kBAAkBhC,UAAU6B,MAAMmC,OAAO;QAC3C;IACF;AACF"}