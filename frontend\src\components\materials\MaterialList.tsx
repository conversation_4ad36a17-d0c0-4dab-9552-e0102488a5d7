"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  materialApi,
  priceApi,
  Material,
  MaterialPrice,
  PaginationResponse,
} from "@/lib/api";
import {
  Pencil,
  Trash2,
  Plus,
  Search,
  Package,
  DollarSign,
} from "lucide-react";
import { toast } from "sonner";
import MaterialForm from "./MaterialForm";

interface MaterialListProps {
  onMaterialSelect?: (material: Material) => void;
  selectable?: boolean;
}

export default function MaterialList({
  onMaterialSelect,
  selectable = false,
}: MaterialListProps) {
  const [materials, setMaterials] = useState<Material[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [pagination, setPagination] = useState<PaginationResponse>({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0,
  });
  const [deleteDialog, setDeleteDialog] = useState<{
    open: boolean;
    material: Material | null;
  }>({
    open: false,
    material: null,
  });

  const [materialForm, setMaterialForm] = useState<{
    open: boolean;
    material: Material | null;
  }>({
    open: false,
    material: null,
  });

  // 加载物料列表
  const loadMaterials = async (page = 1, search = "") => {
    try {
      setLoading(true);
      const response = search
        ? await materialApi.searchMaterials(search, {
            page,
            limit: pagination.limit,
          })
        : await materialApi.getMaterials({ page, limit: pagination.limit });

      if (response.success && response.data) {
        // 获取每个物料的最新价格
        const materialsWithPrices = await Promise.all(
          response.data.materials.map(async (material) => {
            try {
              const priceResponse = await priceApi.getCurrentPrice(material.id);
              return {
                ...material,
                currentPrice: priceResponse.success ? priceResponse.data : null,
              };
            } catch (error) {
              return {
                ...material,
                currentPrice: null,
              };
            }
          })
        );

        setMaterials(materialsWithPrices);
        setPagination(response.data.pagination);
      }
    } catch (error) {
      console.error("Failed to load materials:", error);
      toast.error("加载物料列表失败");
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    loadMaterials();
  }, []);

  // 搜索处理
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    loadMaterials(1, searchQuery);
  };

  // 新增物料
  const handleAdd = () => {
    setMaterialForm({ open: true, material: null });
  };

  // 编辑物料
  const handleEdit = (material: Material) => {
    setMaterialForm({ open: true, material });
  };

  // 删除物料
  const handleDelete = async () => {
    if (!deleteDialog.material) return;

    try {
      const response = await materialApi.deleteMaterial(
        deleteDialog.material.id
      );
      if (response.success) {
        toast.success("物料删除成功");
        loadMaterials(pagination.page, searchQuery);
        setDeleteDialog({ open: false, material: null });
      }
    } catch (error: any) {
      console.error("Failed to delete material:", error);
      const errorMessage = error.response?.data?.message || "删除物料失败";
      toast.error(errorMessage);
    }
  };

  // 表单成功回调
  const handleFormSuccess = () => {
    loadMaterials(pagination.page, searchQuery);
  };

  // 分页处理
  const handlePageChange = (newPage: number) => {
    loadMaterials(newPage, searchQuery);
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("zh-CN");
  };

  // 格式化价格
  const formatPrice = (price: MaterialPrice | null) => {
    if (!price) return "未设置";
    const unitPrice =
      typeof price.unitPrice === "string"
        ? parseFloat(price.unitPrice)
        : price.unitPrice;
    return `${price.currency} ${unitPrice.toFixed(2)}`;
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              物料管理
            </CardTitle>
            <CardDescription>管理物料信息和价格</CardDescription>
          </div>
          <Button onClick={handleAdd}>
            <Plus className="h-4 w-4 mr-2" />
            新增物料
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {/* 搜索栏 */}
        <form onSubmit={handleSearch} className="flex gap-2 mb-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="搜索物料名称..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button type="submit" variant="outline">
            搜索
          </Button>
        </form>

        {/* 物料表格 */}
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>物料名称</TableHead>
                <TableHead>基本单位</TableHead>
                <TableHead>当前价格</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8">
                    加载中...
                  </TableCell>
                </TableRow>
              ) : materials.length === 0 ? (
                <TableRow>
                  <TableCell
                    colSpan={5}
                    className="text-center py-8 text-gray-500"
                  >
                    {searchQuery ? "未找到匹配的物料" : "暂无物料数据"}
                  </TableCell>
                </TableRow>
              ) : (
                materials.map((material) => (
                  <TableRow
                    key={material.id}
                    className={
                      selectable ? "cursor-pointer hover:bg-gray-50" : ""
                    }
                    onClick={
                      selectable
                        ? () => onMaterialSelect?.(material)
                        : undefined
                    }
                  >
                    <TableCell className="font-medium">
                      {material.materialName}
                    </TableCell>
                    <TableCell>{material.baseUnit}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <DollarSign className="h-4 w-4 text-green-600" />
                        {formatPrice((material as any).currentPrice)}
                      </div>
                    </TableCell>
                    <TableCell>{formatDate(material.createdAt)}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(material)}
                        >
                          <Pencil className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            setDeleteDialog({ open: true, material });
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* 分页 */}
        {pagination.pages > 1 && (
          <div className="flex items-center justify-between mt-4">
            <div className="text-sm text-gray-500">
              共 {pagination.total} 条记录，第 {pagination.page} /{" "}
              {pagination.pages} 页
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                disabled={pagination.page <= 1}
                onClick={() => handlePageChange(pagination.page - 1)}
              >
                上一页
              </Button>
              <Button
                variant="outline"
                size="sm"
                disabled={pagination.page >= pagination.pages}
                onClick={() => handlePageChange(pagination.page + 1)}
              >
                下一页
              </Button>
            </div>
          </div>
        )}
      </CardContent>

      {/* 删除确认对话框 */}
      <Dialog
        open={deleteDialog.open}
        onOpenChange={(open) => setDeleteDialog({ open, material: null })}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              确定要删除物料 &quot;{deleteDialog.material?.materialName}&quot;
              吗？此操作不可撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialog({ open: false, material: null })}
            >
              取消
            </Button>
            <Button variant="destructive" onClick={handleDelete}>
              删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 物料表单对话框 */}
      <MaterialForm
        open={materialForm.open}
        onOpenChange={(open) => setMaterialForm({ ...materialForm, open })}
        material={materialForm.material}
        onSuccess={handleFormSuccess}
      />
    </Card>
  );
}
