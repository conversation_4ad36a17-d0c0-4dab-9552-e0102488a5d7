{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/ui/components/shadow-portal.tsx"], "sourcesContent": ["import * as React from 'react'\nimport { createPortal } from 'react-dom'\nimport { STORAGE_KEY_THEME } from '../../shared'\n\nexport function ShadowPortal({ children }: { children: React.ReactNode }) {\n  let portalNode = React.useRef<HTMLElement | null>(null)\n  let shadowNode = React.useRef<ShadowRoot | null>(null)\n  let [, forceUpdate] = React.useState<{} | undefined>()\n\n  // Don't use useLayoutEffect here, as it will cause warnings during SSR in React 18.\n  // Don't use useSyncExternalStore as an SSR gate unless you verified it doesn't\n  // downgrade a Transition of the initial root render to a sync render or\n  // we can assure the root render is not a Transition.\n  React.useEffect(() => {\n    const ownerDocument = document\n    portalNode.current = ownerDocument.createElement('nextjs-portal')\n    // load default color preference from localstorage\n    if (typeof localStorage !== 'undefined') {\n      const theme = localStorage.getItem(STORAGE_KEY_THEME)\n      if (theme === 'dark') {\n        portalNode.current.classList.add('dark')\n        portalNode.current.classList.remove('light')\n      } else if (theme === 'light') {\n        portalNode.current.classList.remove('dark')\n        portalNode.current.classList.add('light')\n      }\n    }\n\n    shadowNode.current = portalNode.current.attachShadow({ mode: 'open' })\n    ownerDocument.body.appendChild(portalNode.current)\n    forceUpdate({})\n    return () => {\n      if (portalNode.current && portalNode.current.ownerDocument) {\n        portalNode.current.ownerDocument.body.removeChild(portalNode.current)\n      }\n    }\n  }, [])\n\n  return shadowNode.current\n    ? createPortal(children, shadowNode.current as any)\n    : null\n}\n"], "names": ["ShadowPort<PERSON>", "children", "portalNode", "React", "useRef", "shadowNode", "forceUpdate", "useState", "useEffect", "ownerDocument", "document", "current", "createElement", "localStorage", "theme", "getItem", "STORAGE_KEY_THEME", "classList", "add", "remove", "attachShadow", "mode", "body", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "createPortal"], "mappings": ";;;;+BAIgBA;;;eAAAA;;;;iEAJO;0BACM;wBACK;AAE3B,SAASA,aAAa,KAA2C;IAA3C,IAAA,EAAEC,QAAQ,EAAiC,GAA3C;IAC3B,IAAIC,aAAaC,OAAMC,MAAM,CAAqB;IAClD,IAAIC,aAAaF,OAAMC,MAAM,CAAoB;IACjD,IAAI,GAAGE,YAAY,GAAGH,OAAMI,QAAQ;IAEpC,oFAAoF;IACpF,+EAA+E;IAC/E,wEAAwE;IACxE,qDAAqD;IACrDJ,OAAMK,SAAS,CAAC;QACd,MAAMC,gBAAgBC;QACtBR,WAAWS,OAAO,GAAGF,cAAcG,aAAa,CAAC;QACjD,kDAAkD;QAClD,IAAI,OAAOC,iBAAiB,aAAa;YACvC,MAAMC,QAAQD,aAAaE,OAAO,CAACC,yBAAiB;YACpD,IAAIF,UAAU,QAAQ;gBACpBZ,WAAWS,OAAO,CAACM,SAAS,CAACC,GAAG,CAAC;gBACjChB,WAAWS,OAAO,CAACM,SAAS,CAACE,MAAM,CAAC;YACtC,OAAO,IAAIL,UAAU,SAAS;gBAC5BZ,WAAWS,OAAO,CAACM,SAAS,CAACE,MAAM,CAAC;gBACpCjB,WAAWS,OAAO,CAACM,SAAS,CAACC,GAAG,CAAC;YACnC;QACF;QAEAb,WAAWM,OAAO,GAAGT,WAAWS,OAAO,CAACS,YAAY,CAAC;YAAEC,MAAM;QAAO;QACpEZ,cAAca,IAAI,CAACC,WAAW,CAACrB,WAAWS,OAAO;QACjDL,YAAY,CAAC;QACb,OAAO;YACL,IAAIJ,WAAWS,OAAO,IAAIT,WAAWS,OAAO,CAACF,aAAa,EAAE;gBAC1DP,WAAWS,OAAO,CAACF,aAAa,CAACa,IAAI,CAACE,WAAW,CAACtB,WAAWS,OAAO;YACtE;QACF;IACF,GAAG,EAAE;IAEL,OAAON,WAAWM,OAAO,iBACrBc,IAAAA,sBAAY,EAACxB,UAAUI,WAAWM,OAAO,IACzC;AACN"}