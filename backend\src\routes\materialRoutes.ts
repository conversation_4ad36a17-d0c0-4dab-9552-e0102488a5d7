import { Router } from 'express';
import {
  getMaterials,
  getMaterialById,
  createMaterial,
  updateMaterial,
  deleteMaterial,
  searchMaterials,
} from '../controllers/materialController';
import { validateRequired, validateMaterial, validatePagination } from '../middleware/validation';

const router = Router();

// GET /api/materials - 获取所有物料（分页）
router.get('/', validatePagination, getMaterials);

// GET /api/materials/search - 搜索物料
router.get('/search', validatePagination, searchMaterials);

// GET /api/materials/:id - 根据ID获取物料
router.get('/:id', getMaterialById);

// POST /api/materials - 创建新物料
router.post(
  '/',
  validateRequired(['materialName', 'baseUnit']),
  validateMaterial,
  createMaterial
);

// PUT /api/materials/:id - 更新物料
router.put('/:id', validateMaterial, updateMaterial);

// DELETE /api/materials/:id - 删除物料
router.delete('/:id', deleteMaterial);

export default router;
