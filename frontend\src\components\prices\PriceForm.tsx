"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { priceApi, materialApi, Material, MaterialPrice } from "@/lib/api";
import { toast } from "sonner";

interface PriceFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  price?: MaterialPrice | null;
  materialId?: string;
  onSuccess: () => void;
}

export default function PriceForm({
  open,
  onOpenChange,
  price,
  materialId,
  onSuccess,
}: PriceFormProps) {
  const [loading, setLoading] = useState(false);
  const [materials, setMaterials] = useState<Material[]>([]);
  const [formData, setFormData] = useState({
    materialId: "",
    unitPrice: "",
    currency: "CNY",
    effectiveDate: "",
  });

  // 加载物料列表
  const loadMaterials = async () => {
    try {
      const response = await materialApi.getMaterials({
        page: 1,
        limit: 100,
        sortBy: "materialName",
        sortOrder: "asc",
      });
      if (response.success && response.data) {
        setMaterials(response.data.materials);
      }
    } catch (error) {
      console.error("Failed to load materials:", error);
      toast.error("加载物料列表失败");
    }
  };

  // 当对话框打开时加载物料列表
  useEffect(() => {
    if (open) {
      loadMaterials();
    }
  }, [open]);

  // 当price或materialId变化时更新表单数据
  useEffect(() => {
    if (price) {
      setFormData({
        materialId: price.materialId,
        unitPrice: price.unitPrice.toString(),
        currency: price.currency,
        effectiveDate: new Date(price.effectiveDate).toISOString().split('T')[0],
      });
    } else if (materialId) {
      setFormData({
        materialId: materialId,
        unitPrice: "",
        currency: "CNY",
        effectiveDate: new Date().toISOString().split('T')[0],
      });
    } else {
      setFormData({
        materialId: "",
        unitPrice: "",
        currency: "CNY",
        effectiveDate: new Date().toISOString().split('T')[0],
      });
    }
  }, [price, materialId]);

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.materialId || !formData.unitPrice || !formData.currency) {
      toast.error("请填写所有必填字段");
      return;
    }

    const unitPrice = parseFloat(formData.unitPrice);
    if (isNaN(unitPrice) || unitPrice <= 0) {
      toast.error("请输入有效的价格");
      return;
    }

    try {
      setLoading(true);
      
      const requestData = {
        materialId: formData.materialId,
        unitPrice: unitPrice,
        currency: formData.currency,
        effectiveDate: formData.effectiveDate || new Date().toISOString(),
      };

      let response;
      if (price) {
        // 更新价格
        response = await priceApi.updateMaterialPrice(price.id, requestData);
      } else {
        // 创建新价格
        response = await priceApi.createMaterialPrice(requestData);
      }

      if (response.success) {
        toast.success(price ? "价格更新成功" : "价格创建成功");
        onSuccess();
      }
    } catch (error: any) {
      console.error("Failed to save price:", error);
      const errorMessage = error.response?.data?.message || 
        (price ? "更新价格失败" : "创建价格失败");
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // 处理输入变化
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // 获取选中物料的信息
  const selectedMaterial = materials.find(m => m.id === formData.materialId);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {price ? "编辑价格" : "新增价格"}
          </DialogTitle>
          <DialogDescription>
            {price ? "修改物料价格信息" : "为物料设置价格信息"}
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* 物料选择 */}
          <div className="space-y-2">
            <Label htmlFor="materialId">物料 *</Label>
            <Select
              value={formData.materialId}
              onValueChange={(value) => handleInputChange("materialId", value)}
              disabled={!!materialId || !!price}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择物料" />
              </SelectTrigger>
              <SelectContent>
                {materials.map((material) => (
                  <SelectItem key={material.id} value={material.id}>
                    {material.materialName} ({material.baseUnit})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 显示选中物料的基本单位 */}
          {selectedMaterial && (
            <div className="space-y-2">
              <Label>基本单位</Label>
              <Input
                value={selectedMaterial.baseUnit}
                disabled
                className="bg-gray-50"
              />
            </div>
          )}

          {/* 单价 */}
          <div className="space-y-2">
            <Label htmlFor="unitPrice">单价 *</Label>
            <Input
              id="unitPrice"
              type="number"
              step="0.01"
              min="0"
              placeholder="请输入单价"
              value={formData.unitPrice}
              onChange={(e) => handleInputChange("unitPrice", e.target.value)}
              required
            />
          </div>

          {/* 货币 */}
          <div className="space-y-2">
            <Label htmlFor="currency">货币 *</Label>
            <Select
              value={formData.currency}
              onValueChange={(value) => handleInputChange("currency", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择货币" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="CNY">人民币 (CNY)</SelectItem>
                <SelectItem value="USD">美元 (USD)</SelectItem>
                <SelectItem value="EUR">欧元 (EUR)</SelectItem>
                <SelectItem value="JPY">日元 (JPY)</SelectItem>
                <SelectItem value="GBP">英镑 (GBP)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* 生效日期 */}
          <div className="space-y-2">
            <Label htmlFor="effectiveDate">生效日期</Label>
            <Input
              id="effectiveDate"
              type="date"
              value={formData.effectiveDate}
              onChange={(e) => handleInputChange("effectiveDate", e.target.value)}
            />
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              取消
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? "保存中..." : (price ? "更新价格" : "创建价格")}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
