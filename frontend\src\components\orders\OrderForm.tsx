"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  orderApi,
  customerApi,
  materialApi,
  priceApi,
  SalesOrder,
  Customer,
  Material,
  CreateSalesOrderRequest,
} from "@/lib/api";
import { Plus, Trash2 } from "lucide-react";
import { toast } from "sonner";

interface OrderFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order?: SalesOrder | null;
  onSuccess: () => void;
}

interface OrderDetail {
  materialId: string;
  materialName: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  totalAmount: number;
}

interface FormData {
  customerId: string;
  customerPoNumber: string;
  orderDetails: OrderDetail[];
}

export default function OrderForm({
  open,
  onOpenChange,
  order,
  onSuccess,
}: OrderFormProps) {
  const [loading, setLoading] = useState(false);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [materials, setMaterials] = useState<Material[]>([]);
  const [formData, setFormData] = useState<FormData>({
    customerId: "",
    customerPoNumber: "",
    orderDetails: [
      {
        materialId: "",
        materialName: "",
        quantity: 0,
        unit: "",
        unitPrice: 0,
        totalAmount: 0,
      },
    ],
  });

  // 加载客户和物料数据
  useEffect(() => {
    if (open) {
      loadCustomers();
      loadMaterials();
    }
  }, [open]);

  // 初始化表单数据
  useEffect(() => {
    if (order) {
      setFormData({
        customerId: order.customerId,
        customerPoNumber: order.customerPoNumber || "",
        orderDetails:
          order.salesOrderDetails?.map((detail) => ({
            materialId: detail.materialId,
            materialName: detail.material?.materialName || "",
            quantity:
              typeof detail.quantity === "string"
                ? parseFloat(detail.quantity)
                : detail.quantity,
            unit: detail.unit,
            unitPrice:
              typeof detail.unitPrice === "string"
                ? parseFloat(detail.unitPrice)
                : detail.unitPrice,
            totalAmount:
              typeof detail.totalAmount === "string"
                ? parseFloat(detail.totalAmount)
                : detail.totalAmount,
          })) || [],
      });
    } else {
      setFormData({
        customerId: "",
        customerPoNumber: "",
        orderDetails: [
          {
            materialId: "",
            materialName: "",
            quantity: 1,
            unit: "",
            unitPrice: 0,
            totalAmount: 0,
          },
        ],
      });
    }
  }, [order]);

  const loadCustomers = async () => {
    try {
      const response = await customerApi.getCustomers({ limit: 100 });
      if (response.success && response.data) {
        setCustomers(response.data.customers);
      }
    } catch (error) {
      console.error("Failed to load customers:", error);
      toast.error("加载客户列表失败");
    }
  };

  const loadMaterials = async () => {
    try {
      const response = await materialApi.getMaterials({ limit: 100 });
      if (response.success && response.data) {
        setMaterials(response.data.materials);
      }
    } catch (error) {
      console.error("Failed to load materials:", error);
      toast.error("加载物料列表失败");
    }
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const addOrderDetail = () => {
    setFormData((prev) => ({
      ...prev,
      orderDetails: [
        ...prev.orderDetails,
        {
          materialId: "",
          materialName: "",
          quantity: 0,
          unit: "",
          unitPrice: 0,
          totalAmount: 0,
        },
      ],
    }));
  };

  const removeOrderDetail = (index: number) => {
    if (formData.orderDetails.length > 1) {
      setFormData((prev) => ({
        ...prev,
        orderDetails: prev.orderDetails.filter((_, i) => i !== index),
      }));
    }
  };

  const updateOrderDetail = async (
    index: number,
    field: keyof OrderDetail,
    value: string | number
  ) => {
    setFormData((prev) => {
      const newDetails = [...prev.orderDetails];
      const detail = { ...newDetails[index] };

      if (field === "materialId") {
        const material = materials.find((m) => m.id === value);
        if (material) {
          detail.materialId = value as string;
          detail.materialName = material.materialName;
          detail.unit = material.baseUnit;

          // 异步获取物料当前价格
          priceApi
            .getCurrentPrice(value as string)
            .then((response) => {
              if (response.success && response.data) {
                setFormData((currentData) => {
                  const currentDetails = [...currentData.orderDetails];
                  const currentDetail = { ...currentDetails[index] };
                  currentDetail.unitPrice =
                    typeof response.data.unitPrice === "string"
                      ? parseFloat(response.data.unitPrice)
                      : response.data.unitPrice;
                  // 只有在有数量和价格时才计算金额
                  if (
                    currentDetail.quantity > 0 &&
                    currentDetail.unitPrice > 0
                  ) {
                    currentDetail.totalAmount =
                      currentDetail.quantity * currentDetail.unitPrice;
                  } else {
                    currentDetail.totalAmount = 0;
                  }
                  currentDetails[index] = currentDetail;
                  return {
                    ...currentData,
                    orderDetails: currentDetails,
                  };
                });
              }
            })
            .catch((error) => {
              console.warn("Failed to load material price:", error);
              // 价格获取失败时不显示错误，让用户手动输入价格
            });
        }
      } else {
        (detail as any)[field] = value;
      }

      // 重新计算总金额 - 只有在有数量和价格时才计算
      if (field === "quantity" || field === "unitPrice") {
        if (detail.quantity > 0 && detail.unitPrice > 0) {
          detail.totalAmount = detail.quantity * detail.unitPrice;
        } else {
          detail.totalAmount = 0;
        }
      }

      newDetails[index] = detail;
      return {
        ...prev,
        orderDetails: newDetails,
      };
    });
  };

  const calculateTotalAmount = () => {
    return formData.orderDetails.reduce((sum, detail) => {
      // 只计算有数量和价格的明细行
      if (detail.quantity > 0 && detail.unitPrice > 0) {
        return sum + detail.totalAmount;
      }
      return sum;
    }, 0);
  };

  // 安全的数字格式化函数
  const formatNumber = (value: number | string) => {
    const numValue = typeof value === "string" ? parseFloat(value) : value;
    return (numValue || 0).toFixed(2);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.customerId) {
      toast.error("请选择客户");
      return;
    }

    if (formData.orderDetails.length === 0) {
      toast.error("请添加至少一个订单明细");
      return;
    }

    // 验证订单明细
    for (const detail of formData.orderDetails) {
      if (!detail.materialId) {
        toast.error("请为所有明细选择物料");
        return;
      }
      if (detail.quantity <= 0) {
        toast.error("数量必须大于0");
        return;
      }
      if (detail.unitPrice <= 0) {
        toast.error("单价必须大于0");
        return;
      }
    }

    try {
      setLoading(true);

      const requestData: CreateSalesOrderRequest = {
        customerId: formData.customerId,
        customerPoNumber: formData.customerPoNumber || undefined,
        orderDetails: formData.orderDetails.map((detail) => ({
          materialId: detail.materialId,
          quantity: detail.quantity,
          unit: detail.unit,
          unitPrice: detail.unitPrice,
        })),
      };

      if (order) {
        // 编辑订单
        const response = await orderApi.updateOrder(order.id, requestData);

        if (response.success) {
          toast.success("订单更新成功");
          onSuccess();
          onOpenChange(false);
        }
      } else {
        // 新增订单
        const response = await orderApi.createOrder(requestData);

        if (response.success) {
          toast.success("订单创建成功");
          onSuccess();
          onOpenChange(false);
        }
      }
    } catch (error: any) {
      console.error("Failed to save order:", error);
      const errorMessage = error.response?.data?.message || "保存订单失败";
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="!max-w-[95vw] !w-[95vw] max-h-[95vh] h-auto overflow-y-auto p-8 sm:!max-w-[95vw]">
        <DialogHeader>
          <DialogTitle>{order ? "编辑订单" : "新增订单"}</DialogTitle>
          <DialogDescription>
            {order ? "修改订单信息和明细" : "创建新的销售订单"}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-8 w-full">
          {/* 基本信息 */}
          <Card className="w-full">
            <CardHeader>
              <CardTitle className="text-lg">基本信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
                <div className="space-y-2">
                  <Label htmlFor="customerId">客户 *</Label>
                  <Select
                    value={formData.customerId}
                    onValueChange={(value) =>
                      handleInputChange("customerId", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择客户" />
                    </SelectTrigger>
                    <SelectContent>
                      {customers.map((customer) => (
                        <SelectItem key={customer.id} value={customer.id}>
                          {customer.customerName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="customerPoNumber">客户订单号</Label>
                  <Input
                    id="customerPoNumber"
                    value={formData.customerPoNumber}
                    onChange={(e) =>
                      handleInputChange("customerPoNumber", e.target.value)
                    }
                    placeholder="输入客户订单号"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 订单明细 */}
          <Card className="w-full">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">订单明细</CardTitle>
                <Button type="button" onClick={addOrderDetail} size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  添加明细
                </Button>
              </div>
            </CardHeader>
            <CardContent className="w-full overflow-x-auto">
              {formData.orderDetails.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  暂无订单明细，请点击"添加明细"按钮添加
                </div>
              ) : (
                <div className="border rounded-lg overflow-x-auto w-full">
                  <Table className="w-full table-auto">
                    <TableHeader>
                      <TableRow>
                        <TableHead className="min-w-[400px] max-w-[600px]">
                          物料名称
                        </TableHead>
                        <TableHead className="w-[220px]">数量</TableHead>
                        <TableHead className="w-[150px]">单位</TableHead>
                        <TableHead className="w-[180px]">单价(¥)</TableHead>
                        <TableHead className="w-[180px]">金额(¥)</TableHead>
                        <TableHead className="w-[120px] text-right">
                          操作
                        </TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {formData.orderDetails.map((detail, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            <Select
                              value={detail.materialId}
                              onValueChange={(value) =>
                                updateOrderDetail(index, "materialId", value)
                              }
                            >
                              <SelectTrigger className="w-full min-w-[380px]">
                                <SelectValue placeholder="选择物料" />
                              </SelectTrigger>
                              <SelectContent>
                                {materials.map((material) => (
                                  <SelectItem
                                    key={material.id}
                                    value={material.id}
                                  >
                                    {material.materialName}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </TableCell>
                          <TableCell>
                            <Input
                              type="text"
                              value={
                                detail.quantity > 0
                                  ? detail.quantity.toString()
                                  : ""
                              }
                              onChange={(e) => {
                                const value = e.target.value;
                                // 只允许输入数字和小数点
                                if (value === "" || /^\d*\.?\d*$/.test(value)) {
                                  updateOrderDetail(
                                    index,
                                    "quantity",
                                    value === "" ? 0 : parseFloat(value) || 0
                                  );
                                }
                              }}
                              placeholder="输入数量"
                              className="w-full text-right min-w-[180px] px-3 py-2 text-base"
                              style={{
                                width: `${Math.max(
                                  180,
                                  (detail.quantity > 0
                                    ? detail.quantity.toString().length
                                    : 4) *
                                    12 +
                                    24
                                )}px`,
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            <div className="px-3 py-2 text-center">
                              {detail.unit || "-"}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="px-3 py-2 text-right font-medium">
                              {detail.unitPrice > 0
                                ? formatNumber(detail.unitPrice)
                                : "-"}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="px-3 py-2 text-right font-medium">
                              {detail.quantity > 0 && detail.unitPrice > 0
                                ? formatNumber(detail.totalAmount)
                                : "-"}
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => removeOrderDetail(index)}
                              disabled={formData.orderDetails.length <= 1}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}

              {/* 总计 */}
              {formData.orderDetails.length > 0 && (
                <div className="flex justify-end mt-4 p-4 bg-gray-50 rounded-lg">
                  <div className="text-lg font-semibold text-blue-600">
                    订单总金额: ¥{formatNumber(calculateTotalAmount())}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <DialogFooter className="flex justify-end space-x-4 pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              size="lg"
            >
              取消
            </Button>
            <Button type="submit" disabled={loading} size="lg">
              {loading ? "保存中..." : order ? "更新订单" : "创建订单"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
