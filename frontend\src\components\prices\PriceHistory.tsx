"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { priceApi, Material, MaterialPrice, PaginationResponse } from "@/lib/api";
import { History, TrendingUp, TrendingDown, Minus } from "lucide-react";
import { toast } from "sonner";

interface PriceHistoryProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  material: Material | null;
}

export default function PriceHistory({
  open,
  onOpenChange,
  material,
}: PriceHistoryProps) {
  const [loading, setLoading] = useState(false);
  const [prices, setPrices] = useState<MaterialPrice[]>([]);
  const [pagination, setPagination] = useState<PaginationResponse>({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0,
  });

  // 加载价格历史
  const loadPriceHistory = async (page = 1) => {
    if (!material) return;

    try {
      setLoading(true);
      const response = await priceApi.getMaterialPrices(material.id, {
        page,
        limit: pagination.limit,
        sortBy: "effectiveDate",
        sortOrder: "desc",
      });

      if (response.success && response.data) {
        setPrices(response.data.prices);
        setPagination(response.data.pagination);
      }
    } catch (error) {
      console.error("Failed to load price history:", error);
      toast.error("加载价格历史失败");
    } finally {
      setLoading(false);
    }
  };

  // 当对话框打开且有物料时加载价格历史
  useEffect(() => {
    if (open && material) {
      loadPriceHistory();
    }
  }, [open, material]);

  // 分页处理
  const handlePageChange = (newPage: number) => {
    loadPriceHistory(newPage);
  };

  // 计算价格变化趋势
  const getPriceTrend = (currentPrice: number, previousPrice?: number) => {
    if (!previousPrice) return null;
    
    const change = currentPrice - previousPrice;
    const changePercent = ((change / previousPrice) * 100).toFixed(1);
    
    if (change > 0) {
      return {
        type: "up",
        icon: TrendingUp,
        text: `+${changePercent}%`,
        color: "text-green-600",
      };
    } else if (change < 0) {
      return {
        type: "down",
        icon: TrendingDown,
        text: `${changePercent}%`,
        color: "text-red-600",
      };
    } else {
      return {
        type: "same",
        icon: Minus,
        text: "0%",
        color: "text-gray-600",
      };
    }
  };

  // 格式化价格
  const formatPrice = (price: MaterialPrice) => {
    return `${price.currency} ${Number(price.unitPrice).toFixed(2)}`;
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            价格历史
          </DialogTitle>
          <DialogDescription>
            {material ? `${material.materialName} 的价格变化历史` : ""}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* 物料信息 */}
          {material && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span className="text-sm text-muted-foreground">物料名称</span>
                  <p className="font-medium">{material.materialName}</p>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground">基本单位</span>
                  <p className="font-medium">{material.baseUnit}</p>
                </div>
              </div>
            </div>
          )}

          {/* 价格历史表格 */}
          <div className="border rounded-lg">
            {loading ? (
              <div className="text-center py-8">
                <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                <p className="mt-2 text-muted-foreground">加载中...</p>
              </div>
            ) : prices.length === 0 ? (
              <div className="text-center py-8">
                <History className="mx-auto h-12 w-12 text-muted-foreground" />
                <p className="mt-2 text-muted-foreground">暂无价格历史记录</p>
              </div>
            ) : (
              <>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>生效日期</TableHead>
                      <TableHead>价格</TableHead>
                      <TableHead>变化趋势</TableHead>
                      <TableHead>创建时间</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {prices.map((price, index) => {
                      const previousPrice = index < prices.length - 1 ? 
                        Number(prices[index + 1].unitPrice) : undefined;
                      const trend = getPriceTrend(Number(price.unitPrice), previousPrice);
                      const TrendIcon = trend?.icon;

                      return (
                        <TableRow key={price.id}>
                          <TableCell className="font-medium">
                            {formatDate(price.effectiveDate)}
                          </TableCell>
                          <TableCell>
                            <span className="font-mono">
                              {formatPrice(price)}
                            </span>
                          </TableCell>
                          <TableCell>
                            {trend && TrendIcon && (
                              <div className={`flex items-center gap-1 ${trend.color}`}>
                                <TrendIcon className="h-4 w-4" />
                                <span className="text-sm">{trend.text}</span>
                              </div>
                            )}
                          </TableCell>
                          <TableCell className="text-muted-foreground">
                            {formatDate(price.createdAt)}
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>

                {/* 分页控件 */}
                {pagination.pages > 1 && (
                  <div className="flex justify-center gap-2 p-4 border-t">
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={pagination.page <= 1}
                      onClick={() => handlePageChange(pagination.page - 1)}
                    >
                      上一页
                    </Button>
                    <span className="flex items-center px-4 text-sm">
                      第 {pagination.page} 页 / 共 {pagination.pages} 页
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={pagination.page >= pagination.pages}
                      onClick={() => handlePageChange(pagination.page + 1)}
                    >
                      下一页
                    </Button>
                  </div>
                )}
              </>
            )}
          </div>

          {/* 统计信息 */}
          {prices.length > 0 && (
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium mb-2">价格统计</h4>
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">历史记录数</span>
                  <p className="font-medium">{pagination.total} 条</p>
                </div>
                <div>
                  <span className="text-muted-foreground">最高价格</span>
                  <p className="font-medium">
                    {formatPrice(prices.reduce((max, price) => 
                      Number(price.unitPrice) > Number(max.unitPrice) ? price : max
                    ))}
                  </p>
                </div>
                <div>
                  <span className="text-muted-foreground">最低价格</span>
                  <p className="font-medium">
                    {formatPrice(prices.reduce((min, price) => 
                      Number(price.unitPrice) < Number(min.unitPrice) ? price : min
                    ))}
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="flex justify-end">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            关闭
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
