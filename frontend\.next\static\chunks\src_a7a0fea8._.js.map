{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/MyProject/MyApp-OrderList/frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/MyProject/MyApp-OrderList/frontend/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/MyProject/MyApp-OrderList/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/MyProject/MyApp-OrderList/frontend/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 197, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/MyProject/MyApp-OrderList/frontend/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 335, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/MyProject/MyApp-OrderList/frontend/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,qKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAhCS;AAkCT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 533, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/MyProject/MyApp-OrderList/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 648, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/MyProject/MyApp-OrderList/frontend/src/lib/api.ts"], "sourcesContent": ["import axios from \"axios\";\n\n// API基础配置\nconst API_BASE_URL =\n  process.env.NEXT_PUBLIC_API_URL || \"http://localhost:5000/api\";\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    \"Content-Type\": \"application/json\",\n  },\n});\n\n// 响应拦截器\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    console.error(\"API Error:\", error.response?.data || error.message);\n    return Promise.reject(error);\n  }\n);\n\n// 类型定义\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  message: string;\n  data?: T;\n}\n\nexport interface PaginationParams {\n  page?: number;\n  limit?: number;\n  sortBy?: string;\n  sortOrder?: \"asc\" | \"desc\";\n}\n\nexport interface PaginationResponse {\n  page: number;\n  limit: number;\n  total: number;\n  pages: number;\n}\n\n// 客户相关类型\nexport interface Customer {\n  id: string;\n  customerName: string;\n  contactPerson: string;\n  address: string;\n  createdAt: string;\n  updatedAt: string;\n  _count?: {\n    salesOrders: number;\n  };\n}\n\nexport interface CreateCustomerRequest {\n  customerName: string;\n  contactPerson: string;\n  address: string;\n}\n\n// 物料相关类型\nexport interface Material {\n  id: string;\n  materialName: string;\n  baseUnit: string;\n  createdAt: string;\n  updatedAt: string;\n  materialPrices?: MaterialPrice[];\n}\n\nexport interface MaterialPrice {\n  id: string;\n  materialId: string;\n  unitPrice: number;\n  currency: string;\n  effectiveDate: string;\n  createdAt: string;\n  updatedAt: string;\n  material?: {\n    materialName: string;\n    baseUnit: string;\n  };\n}\n\nexport interface CreateMaterialRequest {\n  materialName: string;\n  baseUnit: string;\n}\n\nexport interface CreateMaterialPriceRequest {\n  materialId: string;\n  unitPrice: number;\n  currency: string;\n  effectiveDate?: string;\n}\n\n// 订单相关类型\nexport interface SalesOrder {\n  id: string;\n  customerId: string;\n  customerPoNumber?: string;\n  totalAmount: number | string;\n  orderStatus: \"PENDING\" | \"CONFIRMED\" | \"SHIPPED\" | \"DELIVERED\" | \"CANCELLED\";\n  createdAt: string;\n  updatedAt: string;\n  customer?: Customer;\n  salesOrderDetails?: SalesOrderDetail[];\n  _count?: {\n    salesOrderDetails: number;\n  };\n}\n\nexport interface SalesOrderDetail {\n  id: string;\n  orderId: string;\n  materialId: string;\n  quantity: number | string;\n  unit: string;\n  unitPrice: number | string;\n  totalAmount: number | string;\n  createdAt: string;\n  updatedAt: string;\n  material?: {\n    materialName: string;\n    baseUnit: string;\n  };\n}\n\nexport interface CreateSalesOrderRequest {\n  customerId: string;\n  customerPoNumber?: string;\n  orderDetails: {\n    materialId: string;\n    quantity: number;\n    unit: string;\n    unitPrice: number;\n  }[];\n}\n\n// 客户API\nexport const customerApi = {\n  // 获取客户列表\n  getCustomers: async (params?: PaginationParams & { search?: string }) => {\n    const response = await api.get<\n      ApiResponse<{\n        customers: Customer[];\n        pagination: PaginationResponse;\n      }>\n    >(\"/customers\", { params });\n    return response.data;\n  },\n\n  // 获取客户详情\n  getCustomer: async (id: string) => {\n    const response = await api.get<ApiResponse<Customer>>(`/customers/${id}`);\n    return response.data;\n  },\n\n  // 创建客户\n  createCustomer: async (data: CreateCustomerRequest) => {\n    const response = await api.post<ApiResponse<Customer>>(\"/customers\", data);\n    return response.data;\n  },\n\n  // 更新客户\n  updateCustomer: async (id: string, data: Partial<CreateCustomerRequest>) => {\n    const response = await api.put<ApiResponse<Customer>>(\n      `/customers/${id}`,\n      data\n    );\n    return response.data;\n  },\n\n  // 删除客户\n  deleteCustomer: async (id: string) => {\n    const response = await api.delete<ApiResponse>(`/customers/${id}`);\n    return response.data;\n  },\n\n  // 搜索客户\n  searchCustomers: async (query: string, params?: PaginationParams) => {\n    const response = await api.get<\n      ApiResponse<{\n        customers: Customer[];\n        pagination: PaginationResponse;\n      }>\n    >(\"/customers/search\", { params: { q: query, ...params } });\n    return response.data;\n  },\n};\n\n// 物料API\nexport const materialApi = {\n  // 获取物料列表\n  getMaterials: async (params?: PaginationParams & { search?: string }) => {\n    const response = await api.get<\n      ApiResponse<{\n        materials: Material[];\n        pagination: PaginationResponse;\n      }>\n    >(\"/materials\", { params });\n    return response.data;\n  },\n\n  // 获取物料详情\n  getMaterial: async (id: string) => {\n    const response = await api.get<ApiResponse<Material>>(`/materials/${id}`);\n    return response.data;\n  },\n\n  // 创建物料\n  createMaterial: async (data: CreateMaterialRequest) => {\n    const response = await api.post<ApiResponse<Material>>(\"/materials\", data);\n    return response.data;\n  },\n\n  // 更新物料\n  updateMaterial: async (id: string, data: Partial<CreateMaterialRequest>) => {\n    const response = await api.put<ApiResponse<Material>>(\n      `/materials/${id}`,\n      data\n    );\n    return response.data;\n  },\n\n  // 删除物料\n  deleteMaterial: async (id: string) => {\n    const response = await api.delete<ApiResponse>(`/materials/${id}`);\n    return response.data;\n  },\n\n  // 搜索物料\n  searchMaterials: async (query: string, params?: PaginationParams) => {\n    const response = await api.get<\n      ApiResponse<{\n        materials: Material[];\n        pagination: PaginationResponse;\n      }>\n    >(\"/materials/search\", { params: { q: query, ...params } });\n    return response.data;\n  },\n};\n\n// 价格API\nexport const priceApi = {\n  // 获取所有物料的当前价格\n  getAllCurrentPrices: async (\n    params?: PaginationParams & { currency?: string }\n  ) => {\n    const response = await api.get<\n      ApiResponse<{\n        materials: Material[];\n        pagination: PaginationResponse;\n      }>\n    >(\"/prices\", { params });\n    return response.data;\n  },\n\n  // 获取指定物料的价格历史\n  getMaterialPrices: async (materialId: string, params?: PaginationParams) => {\n    const response = await api.get<\n      ApiResponse<{\n        prices: MaterialPrice[];\n        material: Material;\n        pagination: PaginationResponse;\n      }>\n    >(`/prices/material/${materialId}`, { params });\n    return response.data;\n  },\n\n  // 获取指定物料的当前价格\n  getCurrentPrice: async (materialId: string) => {\n    const response = await api.get<ApiResponse<MaterialPrice>>(\n      `/prices/material/${materialId}/current`\n    );\n    return response.data;\n  },\n\n  // 创建物料价格\n  createMaterialPrice: async (data: CreateMaterialPriceRequest) => {\n    const response = await api.post<ApiResponse<MaterialPrice>>(\n      \"/prices\",\n      data\n    );\n    return response.data;\n  },\n\n  // 更新物料价格\n  updateMaterialPrice: async (\n    id: string,\n    data: Partial<CreateMaterialPriceRequest>\n  ) => {\n    const response = await api.put<ApiResponse<MaterialPrice>>(\n      `/prices/${id}`,\n      data\n    );\n    return response.data;\n  },\n\n  // 删除物料价格\n  deleteMaterialPrice: async (id: string) => {\n    const response = await api.delete<ApiResponse>(`/prices/${id}`);\n    return response.data;\n  },\n};\n\n// 订单API\nexport const orderApi = {\n  // 获取订单列表\n  getOrders: async (\n    params?: PaginationParams & { status?: string; customerId?: string }\n  ) => {\n    const response = await api.get<\n      ApiResponse<{\n        orders: SalesOrder[];\n        pagination: PaginationResponse;\n      }>\n    >(\"/orders\", { params });\n    return response.data;\n  },\n\n  // 获取订单详情\n  getOrder: async (id: string) => {\n    const response = await api.get<ApiResponse<SalesOrder>>(`/orders/${id}`);\n    return response.data;\n  },\n\n  // 创建订单\n  createOrder: async (data: CreateSalesOrderRequest) => {\n    const response = await api.post<ApiResponse<SalesOrder>>(\"/orders\", data);\n    return response.data;\n  },\n\n  // 更新订单\n  updateOrder: async (id: string, data: Partial<CreateSalesOrderRequest>) => {\n    const response = await api.put<ApiResponse<SalesOrder>>(\n      `/orders/${id}`,\n      data\n    );\n    return response.data;\n  },\n\n  // 删除订单\n  deleteOrder: async (id: string) => {\n    const response = await api.delete<ApiResponse>(`/orders/${id}`);\n    return response.data;\n  },\n\n  // 搜索订单\n  searchOrders: async (\n    query: string,\n    params?: PaginationParams & { status?: string }\n  ) => {\n    const response = await api.get<\n      ApiResponse<{\n        orders: SalesOrder[];\n        pagination: PaginationResponse;\n      }>\n    >(\"/orders/search\", { params: { q: query, ...params } });\n    return response.data;\n  },\n\n  // 获取订单统计\n  getOrderStatistics: async (params?: {\n    startDate?: string;\n    endDate?: string;\n  }) => {\n    const response = await api.get<\n      ApiResponse<{\n        summary: {\n          totalOrders: number;\n          totalAmount: number;\n          ordersByStatus: Array<{\n            orderStatus: string;\n            _count: { id: number };\n            _sum: { totalAmount: number };\n          }>;\n        };\n        topCustomers: Array<Customer & { totalOrderAmount: number }>;\n        topMaterials: Array<\n          Material & { totalQuantity: number; totalAmount: number }\n        >;\n      }>\n    >(\"/orders/statistics\", { params });\n    return response.data;\n  },\n};\n"], "names": [], "mappings": ";;;;;;AAIE;AAJF;;AAEA,UAAU;AACV,MAAM,eACJ,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;AAErC,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,QAAQ;AACR,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC3B,CAAC,WAAa,UACd,CAAC;IACC,QAAQ,KAAK,CAAC,cAAc,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;IACjE,OAAO,QAAQ,MAAM,CAAC;AACxB;AA2HK,MAAM,cAAc;IACzB,SAAS;IACT,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,GAAG,CAK5B,cAAc;YAAE;QAAO;QACzB,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS;IACT,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAwB,CAAC,WAAW,EAAE,IAAI;QACxE,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAwB,cAAc;QACrE,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,gBAAgB,OAAO,IAAY;QACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAC5B,CAAC,WAAW,EAAE,IAAI,EAClB;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAc,CAAC,WAAW,EAAE,IAAI;QACjE,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,iBAAiB,OAAO,OAAe;QACrC,MAAM,WAAW,MAAM,IAAI,GAAG,CAK5B,qBAAqB;YAAE,QAAQ;gBAAE,GAAG;gBAAO,GAAG,MAAM;YAAC;QAAE;QACzD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,cAAc;IACzB,SAAS;IACT,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,GAAG,CAK5B,cAAc;YAAE;QAAO;QACzB,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS;IACT,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAwB,CAAC,WAAW,EAAE,IAAI;QACxE,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAwB,cAAc;QACrE,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,gBAAgB,OAAO,IAAY;QACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAC5B,CAAC,WAAW,EAAE,IAAI,EAClB;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAc,CAAC,WAAW,EAAE,IAAI;QACjE,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,iBAAiB,OAAO,OAAe;QACrC,MAAM,WAAW,MAAM,IAAI,GAAG,CAK5B,qBAAqB;YAAE,QAAQ;gBAAE,GAAG;gBAAO,GAAG,MAAM;YAAC;QAAE;QACzD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,WAAW;IACtB,cAAc;IACd,qBAAqB,OACnB;QAEA,MAAM,WAAW,MAAM,IAAI,GAAG,CAK5B,WAAW;YAAE;QAAO;QACtB,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,mBAAmB,OAAO,YAAoB;QAC5C,MAAM,WAAW,MAAM,IAAI,GAAG,CAM5B,CAAC,iBAAiB,EAAE,YAAY,EAAE;YAAE;QAAO;QAC7C,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,iBAAiB,OAAO;QACtB,MAAM,WAAW,MAAM,IAAI,GAAG,CAC5B,CAAC,iBAAiB,EAAE,WAAW,QAAQ,CAAC;QAE1C,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS;IACT,qBAAqB,OAAO;QAC1B,MAAM,WAAW,MAAM,IAAI,IAAI,CAC7B,WACA;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS;IACT,qBAAqB,OACnB,IACA;QAEA,MAAM,WAAW,MAAM,IAAI,GAAG,CAC5B,CAAC,QAAQ,EAAE,IAAI,EACf;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS;IACT,qBAAqB,OAAO;QAC1B,MAAM,WAAW,MAAM,IAAI,MAAM,CAAc,CAAC,QAAQ,EAAE,IAAI;QAC9D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,WAAW;IACtB,SAAS;IACT,WAAW,OACT;QAEA,MAAM,WAAW,MAAM,IAAI,GAAG,CAK5B,WAAW;YAAE;QAAO;QACtB,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS;IACT,UAAU,OAAO;QACf,MAAM,WAAW,MAAM,IAAI,GAAG,CAA0B,CAAC,QAAQ,EAAE,IAAI;QACvE,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,IAAI,IAAI,CAA0B,WAAW;QACpE,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,aAAa,OAAO,IAAY;QAC9B,MAAM,WAAW,MAAM,IAAI,GAAG,CAC5B,CAAC,QAAQ,EAAE,IAAI,EACf;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAc,CAAC,QAAQ,EAAE,IAAI;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,cAAc,OACZ,OACA;QAEA,MAAM,WAAW,MAAM,IAAI,GAAG,CAK5B,kBAAkB;YAAE,QAAQ;gBAAE,GAAG;gBAAO,GAAG,MAAM;YAAC;QAAE;QACtD,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS;IACT,oBAAoB,OAAO;QAIzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAgB5B,sBAAsB;YAAE;QAAO;QACjC,OAAO,SAAS,IAAI;IACtB;AACF", "debugId": null}}, {"offset": {"line": 839, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/MyProject/MyApp-OrderList/frontend/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 873, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/MyProject/MyApp-OrderList/frontend/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 904, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/MyProject/MyApp-OrderList/frontend/src/components/customers/CustomerForm.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog';\nimport { customerApi, Customer } from '@/lib/api';\nimport { toast } from 'sonner';\n\ninterface CustomerFormProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n  customer?: Customer | null;\n  onSuccess: () => void;\n}\n\nexport default function CustomerForm({ \n  open, \n  onOpenChange, \n  customer, \n  onSuccess \n}: CustomerFormProps) {\n  const [loading, setLoading] = useState(false);\n  const [formData, setFormData] = useState({\n    customerName: '',\n    contactPerson: '',\n    address: '',\n  });\n\n  // 当customer变化时更新表单数据\n  useEffect(() => {\n    if (customer) {\n      setFormData({\n        customerName: customer.customerName,\n        contactPerson: customer.contactPerson || '',\n        address: customer.address || '',\n      });\n    } else {\n      setFormData({\n        customerName: '',\n        contactPerson: '',\n        address: '',\n      });\n    }\n  }, [customer]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!formData.customerName.trim()) {\n      toast.error('请填写客户名称');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      \n      if (customer) {\n        // 编辑客户\n        const response = await customerApi.updateCustomer(customer.id, {\n          customerName: formData.customerName,\n          contactPerson: formData.contactPerson || undefined,\n          address: formData.address || undefined,\n        });\n        \n        if (response.success) {\n          toast.success('客户更新成功');\n          onSuccess();\n          onOpenChange(false);\n        }\n      } else {\n        // 新增客户\n        const response = await customerApi.createCustomer({\n          customerName: formData.customerName,\n          contactPerson: formData.contactPerson || undefined,\n          address: formData.address || undefined,\n        });\n        \n        if (response.success) {\n          toast.success('客户创建成功');\n          onSuccess();\n          onOpenChange(false);\n        }\n      }\n    } catch (error: any) {\n      console.error('Failed to save customer:', error);\n      const errorMessage = error.response?.data?.message || '保存客户失败';\n      toast.error(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (field: string, value: string) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value,\n    }));\n  };\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"sm:max-w-[425px]\">\n        <DialogHeader>\n          <DialogTitle>\n            {customer ? '编辑客户' : '新增客户'}\n          </DialogTitle>\n          <DialogDescription>\n            {customer ? '修改客户信息' : '添加新的客户信息'}\n          </DialogDescription>\n        </DialogHeader>\n        \n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"customerName\">客户名称 *</Label>\n            <Input\n              id=\"customerName\"\n              value={formData.customerName}\n              onChange={(e) => handleInputChange('customerName', e.target.value)}\n              placeholder=\"请输入客户名称\"\n              required\n            />\n          </div>\n          \n          <div className=\"space-y-2\">\n            <Label htmlFor=\"contactPerson\">联系人</Label>\n            <Input\n              id=\"contactPerson\"\n              value={formData.contactPerson}\n              onChange={(e) => handleInputChange('contactPerson', e.target.value)}\n              placeholder=\"请输入联系人姓名\"\n            />\n          </div>\n          \n          <div className=\"space-y-2\">\n            <Label htmlFor=\"address\">地址</Label>\n            <Textarea\n              id=\"address\"\n              value={formData.address}\n              onChange={(e) => handleInputChange('address', e.target.value)}\n              placeholder=\"请输入客户地址\"\n              rows={3}\n            />\n          </div>\n          \n          <DialogFooter>\n            <Button \n              type=\"button\" \n              variant=\"outline\" \n              onClick={() => onOpenChange(false)}\n              disabled={loading}\n            >\n              取消\n            </Button>\n            <Button type=\"submit\" disabled={loading}>\n              {loading ? '保存中...' : (customer ? '更新' : '创建')}\n            </Button>\n          </DialogFooter>\n        </form>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAQA;AACA;;;AAhBA;;;;;;;;;AAyBe,SAAS,aAAa,EACnC,IAAI,EACJ,YAAY,EACZ,QAAQ,EACR,SAAS,EACS;;IAClB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,cAAc;QACd,eAAe;QACf,SAAS;IACX;IAEA,qBAAqB;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,UAAU;gBACZ,YAAY;oBACV,cAAc,SAAS,YAAY;oBACnC,eAAe,SAAS,aAAa,IAAI;oBACzC,SAAS,SAAS,OAAO,IAAI;gBAC/B;YACF,OAAO;gBACL,YAAY;oBACV,cAAc;oBACd,eAAe;oBACf,SAAS;gBACX;YACF;QACF;iCAAG;QAAC;KAAS;IAEb,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS,YAAY,CAAC,IAAI,IAAI;YACjC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,WAAW;YAEX,IAAI,UAAU;gBACZ,OAAO;gBACP,MAAM,WAAW,MAAM,oHAAA,CAAA,cAAW,CAAC,cAAc,CAAC,SAAS,EAAE,EAAE;oBAC7D,cAAc,SAAS,YAAY;oBACnC,eAAe,SAAS,aAAa,IAAI;oBACzC,SAAS,SAAS,OAAO,IAAI;gBAC/B;gBAEA,IAAI,SAAS,OAAO,EAAE;oBACpB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBACd;oBACA,aAAa;gBACf;YACF,OAAO;gBACL,OAAO;gBACP,MAAM,WAAW,MAAM,oHAAA,CAAA,cAAW,CAAC,cAAc,CAAC;oBAChD,cAAc,SAAS,YAAY;oBACnC,eAAe,SAAS,aAAa,IAAI;oBACzC,SAAS,SAAS,OAAO,IAAI;gBAC/B;gBAEA,IAAI,SAAS,OAAO,EAAE;oBACpB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBACd;oBACA,aAAa;gBACf;YACF;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WAAW;YACtD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC,OAAe;QACxC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,cAAW;sCACT,WAAW,SAAS;;;;;;sCAEvB,6LAAC,qIAAA,CAAA,oBAAiB;sCACf,WAAW,WAAW;;;;;;;;;;;;8BAI3B,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAe;;;;;;8CAC9B,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO,SAAS,YAAY;oCAC5B,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCACjE,aAAY;oCACZ,QAAQ;;;;;;;;;;;;sCAIZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAgB;;;;;;8CAC/B,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO,SAAS,aAAa;oCAC7B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;oCAClE,aAAY;;;;;;;;;;;;sCAIhB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAU;;;;;;8CACzB,6LAAC,uIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,OAAO,SAAS,OAAO;oCACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oCAC5D,aAAY;oCACZ,MAAM;;;;;;;;;;;;sCAIV,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS,IAAM,aAAa;oCAC5B,UAAU;8CACX;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,UAAU;8CAC7B,UAAU,WAAY,WAAW,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvD;GAlJwB;KAAA", "debugId": null}}, {"offset": {"line": 1176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/MyProject/MyApp-OrderList/frontend/src/components/customers/CustomerList.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from \"@/components/ui/table\";\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from \"@/components/ui/dialog\";\nimport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n} from \"@/components/ui/card\";\nimport { customerApi, Customer, PaginationResponse } from \"@/lib/api\";\nimport { Pencil, Trash2, Plus, Search, Users } from \"lucide-react\";\nimport { toast } from \"sonner\";\nimport CustomerForm from \"./CustomerForm\";\n\ninterface CustomerListProps {\n  onCustomerSelect?: (customer: Customer) => void;\n  selectable?: boolean;\n}\n\nexport default function CustomerList({\n  onCustomerSelect,\n  selectable = false,\n}: CustomerListProps) {\n  const [customers, setCustomers] = useState<Customer[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [pagination, setPagination] = useState<PaginationResponse>({\n    page: 1,\n    limit: 10,\n    total: 0,\n    pages: 0,\n  });\n  const [deleteDialog, setDeleteDialog] = useState<{\n    open: boolean;\n    customer: Customer | null;\n  }>({\n    open: false,\n    customer: null,\n  });\n\n  const [customerForm, setCustomerForm] = useState<{\n    open: boolean;\n    customer: Customer | null;\n  }>({\n    open: false,\n    customer: null,\n  });\n\n  // 加载客户列表\n  const loadCustomers = async (page = 1, search = \"\") => {\n    try {\n      setLoading(true);\n      const response = search\n        ? await customerApi.searchCustomers(search, {\n            page,\n            limit: pagination.limit,\n          })\n        : await customerApi.getCustomers({ page, limit: pagination.limit });\n\n      if (response.success && response.data) {\n        setCustomers(response.data.customers);\n        setPagination(response.data.pagination);\n      }\n    } catch (error) {\n      console.error(\"Failed to load customers:\", error);\n      toast.error(\"加载客户列表失败\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 初始加载\n  useEffect(() => {\n    loadCustomers();\n  }, []);\n\n  // 搜索处理\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    loadCustomers(1, searchQuery);\n  };\n\n  // 新增客户\n  const handleAdd = () => {\n    setCustomerForm({ open: true, customer: null });\n  };\n\n  // 编辑客户\n  const handleEdit = (customer: Customer) => {\n    setCustomerForm({ open: true, customer });\n  };\n\n  // 删除客户\n  const handleDelete = async () => {\n    if (!deleteDialog.customer) return;\n\n    try {\n      const response = await customerApi.deleteCustomer(\n        deleteDialog.customer.id\n      );\n      if (response.success) {\n        toast.success(\"客户删除成功\");\n        loadCustomers(pagination.page, searchQuery);\n        setDeleteDialog({ open: false, customer: null });\n      }\n    } catch (error: any) {\n      console.error(\"Failed to delete customer:\", error);\n      const errorMessage = error.response?.data?.message || \"删除客户失败\";\n      toast.error(errorMessage);\n    }\n  };\n\n  // 表单成功回调\n  const handleFormSuccess = () => {\n    loadCustomers(pagination.page, searchQuery);\n  };\n\n  // 分页处理\n  const handlePageChange = (newPage: number) => {\n    loadCustomers(newPage, searchQuery);\n  };\n\n  // 格式化日期\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString(\"zh-CN\");\n  };\n\n  return (\n    <Card>\n      <CardHeader>\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Users className=\"h-5 w-5\" />\n              客户管理\n            </CardTitle>\n            <CardDescription>管理客户信息和联系方式</CardDescription>\n          </div>\n          <Button onClick={handleAdd}>\n            <Plus className=\"h-4 w-4 mr-2\" />\n            新增客户\n          </Button>\n        </div>\n      </CardHeader>\n      <CardContent>\n        {/* 搜索栏 */}\n        <form onSubmit={handleSearch} className=\"flex gap-2 mb-4\">\n          <div className=\"relative flex-1\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n            <Input\n              placeholder=\"搜索客户名称或联系人...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"pl-10\"\n            />\n          </div>\n          <Button type=\"submit\" variant=\"outline\">\n            搜索\n          </Button>\n        </form>\n\n        {/* 客户表格 */}\n        <div className=\"border rounded-lg\">\n          <Table>\n            <TableHeader>\n              <TableRow>\n                <TableHead>客户名称</TableHead>\n                <TableHead>联系人</TableHead>\n                <TableHead>地址</TableHead>\n                <TableHead>订单数量</TableHead>\n                <TableHead>创建时间</TableHead>\n                <TableHead className=\"text-right\">操作</TableHead>\n              </TableRow>\n            </TableHeader>\n            <TableBody>\n              {loading ? (\n                <TableRow>\n                  <TableCell colSpan={6} className=\"text-center py-8\">\n                    加载中...\n                  </TableCell>\n                </TableRow>\n              ) : customers.length === 0 ? (\n                <TableRow>\n                  <TableCell\n                    colSpan={6}\n                    className=\"text-center py-8 text-gray-500\"\n                  >\n                    {searchQuery ? \"未找到匹配的客户\" : \"暂无客户数据\"}\n                  </TableCell>\n                </TableRow>\n              ) : (\n                customers.map((customer) => (\n                  <TableRow\n                    key={customer.id}\n                    className={\n                      selectable ? \"cursor-pointer hover:bg-gray-50\" : \"\"\n                    }\n                    onClick={\n                      selectable\n                        ? () => onCustomerSelect?.(customer)\n                        : undefined\n                    }\n                  >\n                    <TableCell className=\"font-medium\">\n                      {customer.customerName}\n                    </TableCell>\n                    <TableCell>{customer.contactPerson}</TableCell>\n                    <TableCell>{customer.address}</TableCell>\n                    <TableCell>{customer._count?.salesOrders || 0}</TableCell>\n                    <TableCell>{formatDate(customer.createdAt)}</TableCell>\n                    <TableCell className=\"text-right\">\n                      <div className=\"flex justify-end gap-2\">\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => handleEdit(customer)}\n                        >\n                          <Pencil className=\"h-4 w-4\" />\n                        </Button>\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={(e) => {\n                            e.stopPropagation();\n                            setDeleteDialog({ open: true, customer });\n                          }}\n                        >\n                          <Trash2 className=\"h-4 w-4\" />\n                        </Button>\n                      </div>\n                    </TableCell>\n                  </TableRow>\n                ))\n              )}\n            </TableBody>\n          </Table>\n        </div>\n\n        {/* 分页 */}\n        {pagination.pages > 1 && (\n          <div className=\"flex items-center justify-between mt-4\">\n            <div className=\"text-sm text-gray-500\">\n              共 {pagination.total} 条记录，第 {pagination.page} /{\" \"}\n              {pagination.pages} 页\n            </div>\n            <div className=\"flex gap-2\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                disabled={pagination.page <= 1}\n                onClick={() => handlePageChange(pagination.page - 1)}\n              >\n                上一页\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                disabled={pagination.page >= pagination.pages}\n                onClick={() => handlePageChange(pagination.page + 1)}\n              >\n                下一页\n              </Button>\n            </div>\n          </div>\n        )}\n      </CardContent>\n\n      {/* 删除确认对话框 */}\n      <Dialog\n        open={deleteDialog.open}\n        onOpenChange={(open) => setDeleteDialog({ open, customer: null })}\n      >\n        <DialogContent>\n          <DialogHeader>\n            <DialogTitle>确认删除</DialogTitle>\n            <DialogDescription>\n              确定要删除客户 &quot;{deleteDialog.customer?.customerName}&quot;\n              吗？此操作不可撤销。\n            </DialogDescription>\n          </DialogHeader>\n          <DialogFooter>\n            <Button\n              variant=\"outline\"\n              onClick={() => setDeleteDialog({ open: false, customer: null })}\n            >\n              取消\n            </Button>\n            <Button variant=\"destructive\" onClick={handleDelete}>\n              删除\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* 客户表单对话框 */}\n      <CustomerForm\n        open={customerForm.open}\n        onOpenChange={(open) => setCustomerForm({ ...customerForm, open })}\n        customer={customerForm.customer}\n        onSuccess={handleFormSuccess}\n      />\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAQA;AAQA;AAOA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AA/BA;;;;;;;;;;;AAsCe,SAAS,aAAa,EACnC,gBAAgB,EAChB,aAAa,KAAK,EACA;;IAClB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;QAC/D,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAG5C;QACD,MAAM;QACN,UAAU;IACZ;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAG5C;QACD,MAAM;QACN,UAAU;IACZ;IAEA,SAAS;IACT,MAAM,gBAAgB,OAAO,OAAO,CAAC,EAAE,SAAS,EAAE;QAChD,IAAI;YACF,WAAW;YACX,MAAM,WAAW,SACb,MAAM,oHAAA,CAAA,cAAW,CAAC,eAAe,CAAC,QAAQ;gBACxC;gBACA,OAAO,WAAW,KAAK;YACzB,KACA,MAAM,oHAAA,CAAA,cAAW,CAAC,YAAY,CAAC;gBAAE;gBAAM,OAAO,WAAW,KAAK;YAAC;YAEnE,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,aAAa,SAAS,IAAI,CAAC,SAAS;gBACpC,cAAc,SAAS,IAAI,CAAC,UAAU;YACxC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,OAAO;IACP,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG,EAAE;IAEL,OAAO;IACP,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,cAAc,GAAG;IACnB;IAEA,OAAO;IACP,MAAM,YAAY;QAChB,gBAAgB;YAAE,MAAM;YAAM,UAAU;QAAK;IAC/C;IAEA,OAAO;IACP,MAAM,aAAa,CAAC;QAClB,gBAAgB;YAAE,MAAM;YAAM;QAAS;IACzC;IAEA,OAAO;IACP,MAAM,eAAe;QACnB,IAAI,CAAC,aAAa,QAAQ,EAAE;QAE5B,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,cAAW,CAAC,cAAc,CAC/C,aAAa,QAAQ,CAAC,EAAE;YAE1B,IAAI,SAAS,OAAO,EAAE;gBACpB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,cAAc,WAAW,IAAI,EAAE;gBAC/B,gBAAgB;oBAAE,MAAM;oBAAO,UAAU;gBAAK;YAChD;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WAAW;YACtD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,SAAS;IACT,MAAM,oBAAoB;QACxB,cAAc,WAAW,IAAI,EAAE;IACjC;IAEA,OAAO;IACP,MAAM,mBAAmB,CAAC;QACxB,cAAc,SAAS;IACzB;IAEA,QAAQ;IACR,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC;IACjD;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;;0BACH,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAG/B,6LAAC,mIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAEnB,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS;;8CACf,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;0BAKvC,6LAAC,mIAAA,CAAA,cAAW;;kCAEV,6LAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC,oIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;0CAGd,6LAAC,qIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAS,SAAQ;0CAAU;;;;;;;;;;;;kCAM1C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;;8CACJ,6LAAC,oIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;0DACP,6LAAC,oIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,oIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,oIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,oIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,oIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,oIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAa;;;;;;;;;;;;;;;;;8CAGtC,6LAAC,oIAAA,CAAA,YAAS;8CACP,wBACC,6LAAC,oIAAA,CAAA,WAAQ;kDACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;4CAAC,SAAS;4CAAG,WAAU;sDAAmB;;;;;;;;;;+CAIpD,UAAU,MAAM,KAAK,kBACvB,6LAAC,oIAAA,CAAA,WAAQ;kDACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;4CACR,SAAS;4CACT,WAAU;sDAET,cAAc,aAAa;;;;;;;;;;+CAIhC,UAAU,GAAG,CAAC,CAAC,yBACb,6LAAC,oIAAA,CAAA,WAAQ;4CAEP,WACE,aAAa,oCAAoC;4CAEnD,SACE,aACI,IAAM,mBAAmB,YACzB;;8DAGN,6LAAC,oIAAA,CAAA,YAAS;oDAAC,WAAU;8DAClB,SAAS,YAAY;;;;;;8DAExB,6LAAC,oIAAA,CAAA,YAAS;8DAAE,SAAS,aAAa;;;;;;8DAClC,6LAAC,oIAAA,CAAA,YAAS;8DAAE,SAAS,OAAO;;;;;;8DAC5B,6LAAC,oIAAA,CAAA,YAAS;8DAAE,SAAS,MAAM,EAAE,eAAe;;;;;;8DAC5C,6LAAC,oIAAA,CAAA,YAAS;8DAAE,WAAW,SAAS,SAAS;;;;;;8DACzC,6LAAC,oIAAA,CAAA,YAAS;oDAAC,WAAU;8DACnB,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,WAAW;0EAE1B,cAAA,6LAAC,yMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;0EAEpB,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,CAAC;oEACR,EAAE,eAAe;oEACjB,gBAAgB;wEAAE,MAAM;wEAAM;oEAAS;gEACzC;0EAEA,cAAA,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2CAlCnB,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;oBA8C3B,WAAW,KAAK,GAAG,mBAClB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCAAwB;oCAClC,WAAW,KAAK;oCAAC;oCAAQ,WAAW,IAAI;oCAAC;oCAAG;oCAC9C,WAAW,KAAK;oCAAC;;;;;;;0CAEpB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,UAAU,WAAW,IAAI,IAAI;wCAC7B,SAAS,IAAM,iBAAiB,WAAW,IAAI,GAAG;kDACnD;;;;;;kDAGD,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,UAAU,WAAW,IAAI,IAAI,WAAW,KAAK;wCAC7C,SAAS,IAAM,iBAAiB,WAAW,IAAI,GAAG;kDACnD;;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC,qIAAA,CAAA,SAAM;gBACL,MAAM,aAAa,IAAI;gBACvB,cAAc,CAAC,OAAS,gBAAgB;wBAAE;wBAAM,UAAU;oBAAK;0BAE/D,cAAA,6LAAC,qIAAA,CAAA,gBAAa;;sCACZ,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,6LAAC,qIAAA,CAAA,oBAAiB;;wCAAC;wCACF,aAAa,QAAQ,EAAE;wCAAa;;;;;;;;;;;;;sCAIvD,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,gBAAgB;4CAAE,MAAM;4CAAO,UAAU;wCAAK;8CAC9D;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAc,SAAS;8CAAc;;;;;;;;;;;;;;;;;;;;;;;0BAQ3D,6LAAC,kJAAA,CAAA,UAAY;gBACX,MAAM,aAAa,IAAI;gBACvB,cAAc,CAAC,OAAS,gBAAgB;wBAAE,GAAG,YAAY;wBAAE;oBAAK;gBAChE,UAAU,aAAa,QAAQ;gBAC/B,WAAW;;;;;;;;;;;;AAInB;GA5RwB;KAAA", "debugId": null}}, {"offset": {"line": 1793, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/MyProject/MyApp-OrderList/frontend/src/components/materials/MaterialForm.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from \"@/components/ui/dialog\";\nimport { materialApi, priceApi, Material } from \"@/lib/api\";\nimport { toast } from \"sonner\";\n\ninterface MaterialFormProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n  material?: Material | null;\n  onSuccess: () => void;\n}\n\nexport default function MaterialForm({\n  open,\n  onOpenChange,\n  material,\n  onSuccess,\n}: MaterialFormProps) {\n  const [loading, setLoading] = useState(false);\n  const [formData, setFormData] = useState({\n    materialName: \"\",\n    baseUnit: \"\",\n    unitPrice: \"\",\n    currency: \"CNY\",\n  });\n\n  // 当material变化时更新表单数据\n  useEffect(() => {\n    if (material) {\n      setFormData({\n        materialName: material.materialName,\n        baseUnit: material.baseUnit,\n        unitPrice: \"\",\n        currency: \"CNY\",\n      });\n    } else {\n      setFormData({\n        materialName: \"\",\n        baseUnit: \"\",\n        unitPrice: \"\",\n        currency: \"CNY\",\n      });\n    }\n  }, [material]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!formData.materialName.trim() || !formData.baseUnit.trim()) {\n      toast.error(\"请填写物料名称和基本单位\");\n      return;\n    }\n\n    try {\n      setLoading(true);\n\n      if (material) {\n        // 编辑物料\n        const response = await materialApi.updateMaterial(material.id, {\n          materialName: formData.materialName,\n          baseUnit: formData.baseUnit,\n        });\n\n        if (response.success) {\n          // 如果有价格信息，更新价格\n          if (formData.unitPrice && parseFloat(formData.unitPrice) > 0) {\n            await priceApi.createMaterialPrice({\n              materialId: material.id,\n              unitPrice: parseFloat(formData.unitPrice),\n              currency: formData.currency,\n            });\n          }\n\n          toast.success(\"物料更新成功\");\n          onSuccess();\n          onOpenChange(false);\n        }\n      } else {\n        // 新增物料\n        const response = await materialApi.createMaterial({\n          materialName: formData.materialName,\n          baseUnit: formData.baseUnit,\n        });\n\n        if (response.success && response.data) {\n          // 如果有价格信息，创建价格记录\n          if (formData.unitPrice && parseFloat(formData.unitPrice) > 0) {\n            await priceApi.createMaterialPrice({\n              materialId: response.data.id,\n              unitPrice: parseFloat(formData.unitPrice),\n              currency: formData.currency,\n            });\n          }\n\n          toast.success(\"物料创建成功\");\n          onSuccess();\n          onOpenChange(false);\n        }\n      }\n    } catch (error: any) {\n      console.error(\"Failed to save material:\", error);\n      const errorMessage = error.response?.data?.message || \"保存物料失败\";\n      toast.error(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (field: string, value: string) => {\n    setFormData((prev) => ({\n      ...prev,\n      [field]: value,\n    }));\n  };\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"sm:max-w-[425px]\">\n        <DialogHeader>\n          <DialogTitle>{material ? \"编辑物料\" : \"新增物料\"}</DialogTitle>\n          <DialogDescription>\n            {material ? \"修改物料信息和价格\" : \"添加新的物料信息和价格\"}\n          </DialogDescription>\n        </DialogHeader>\n\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"materialName\">物料名称 *</Label>\n            <Input\n              id=\"materialName\"\n              value={formData.materialName}\n              onChange={(e) =>\n                handleInputChange(\"materialName\", e.target.value)\n              }\n              placeholder=\"请输入物料名称\"\n              required\n            />\n          </div>\n\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"baseUnit\">基本单位 *</Label>\n            <Input\n              id=\"baseUnit\"\n              value={formData.baseUnit}\n              onChange={(e) => handleInputChange(\"baseUnit\", e.target.value)}\n              placeholder=\"如：个、吨、米等\"\n              required\n            />\n          </div>\n\n          <div className=\"grid grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"unitPrice\">单价</Label>\n              <Input\n                id=\"unitPrice\"\n                type=\"number\"\n                step=\"0.01\"\n                min=\"0\"\n                value={formData.unitPrice}\n                onChange={(e) => handleInputChange(\"unitPrice\", e.target.value)}\n                placeholder=\"0.00\"\n              />\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"currency\">币别</Label>\n              <select\n                id=\"currency\"\n                value={formData.currency}\n                onChange={(e) => handleInputChange(\"currency\", e.target.value)}\n                className=\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\"\n              >\n                <option value=\"CNY\">人民币</option>\n                <option value=\"USD\">美元</option>\n                <option value=\"EUR\">欧元</option>\n              </select>\n            </div>\n          </div>\n\n          <DialogFooter>\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={() => onOpenChange(false)}\n              disabled={loading}\n            >\n              取消\n            </Button>\n            <Button type=\"submit\" disabled={loading}>\n              {loading ? \"保存中...\" : material ? \"更新\" : \"创建\"}\n            </Button>\n          </DialogFooter>\n        </form>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAQA;AACA;;;AAfA;;;;;;;;AAwBe,SAAS,aAAa,EACnC,IAAI,EACJ,YAAY,EACZ,QAAQ,EACR,SAAS,EACS;;IAClB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,cAAc;QACd,UAAU;QACV,WAAW;QACX,UAAU;IACZ;IAEA,qBAAqB;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,UAAU;gBACZ,YAAY;oBACV,cAAc,SAAS,YAAY;oBACnC,UAAU,SAAS,QAAQ;oBAC3B,WAAW;oBACX,UAAU;gBACZ;YACF,OAAO;gBACL,YAAY;oBACV,cAAc;oBACd,UAAU;oBACV,WAAW;oBACX,UAAU;gBACZ;YACF;QACF;iCAAG;QAAC;KAAS;IAEb,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS,YAAY,CAAC,IAAI,MAAM,CAAC,SAAS,QAAQ,CAAC,IAAI,IAAI;YAC9D,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,WAAW;YAEX,IAAI,UAAU;gBACZ,OAAO;gBACP,MAAM,WAAW,MAAM,oHAAA,CAAA,cAAW,CAAC,cAAc,CAAC,SAAS,EAAE,EAAE;oBAC7D,cAAc,SAAS,YAAY;oBACnC,UAAU,SAAS,QAAQ;gBAC7B;gBAEA,IAAI,SAAS,OAAO,EAAE;oBACpB,eAAe;oBACf,IAAI,SAAS,SAAS,IAAI,WAAW,SAAS,SAAS,IAAI,GAAG;wBAC5D,MAAM,oHAAA,CAAA,WAAQ,CAAC,mBAAmB,CAAC;4BACjC,YAAY,SAAS,EAAE;4BACvB,WAAW,WAAW,SAAS,SAAS;4BACxC,UAAU,SAAS,QAAQ;wBAC7B;oBACF;oBAEA,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBACd;oBACA,aAAa;gBACf;YACF,OAAO;gBACL,OAAO;gBACP,MAAM,WAAW,MAAM,oHAAA,CAAA,cAAW,CAAC,cAAc,CAAC;oBAChD,cAAc,SAAS,YAAY;oBACnC,UAAU,SAAS,QAAQ;gBAC7B;gBAEA,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;oBACrC,iBAAiB;oBACjB,IAAI,SAAS,SAAS,IAAI,WAAW,SAAS,SAAS,IAAI,GAAG;wBAC5D,MAAM,oHAAA,CAAA,WAAQ,CAAC,mBAAmB,CAAC;4BACjC,YAAY,SAAS,IAAI,CAAC,EAAE;4BAC5B,WAAW,WAAW,SAAS,SAAS;4BACxC,UAAU,SAAS,QAAQ;wBAC7B;oBACF;oBAEA,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBACd;oBACA,aAAa;gBACf;YACF;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WAAW;YACtD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC,OAAe;QACxC,YAAY,CAAC,OAAS,CAAC;gBACrB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,cAAW;sCAAE,WAAW,SAAS;;;;;;sCAClC,6LAAC,qIAAA,CAAA,oBAAiB;sCACf,WAAW,cAAc;;;;;;;;;;;;8BAI9B,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAe;;;;;;8CAC9B,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO,SAAS,YAAY;oCAC5B,UAAU,CAAC,IACT,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCAElD,aAAY;oCACZ,QAAQ;;;;;;;;;;;;sCAIZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAW;;;;;;8CAC1B,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO,SAAS,QAAQ;oCACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC7D,aAAY;oCACZ,QAAQ;;;;;;;;;;;;sCAIZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAY;;;;;;sDAC3B,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,KAAI;4CACJ,OAAO,SAAS,SAAS;4CACzB,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;4CAC9D,aAAY;;;;;;;;;;;;8CAIhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAW;;;;;;sDAC1B,6LAAC;4CACC,IAAG;4CACH,OAAO,SAAS,QAAQ;4CACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC7D,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAM;;;;;;;;;;;;;;;;;;;;;;;;sCAK1B,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS,IAAM,aAAa;oCAC5B,UAAU;8CACX;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,UAAU;8CAC7B,UAAU,WAAW,WAAW,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtD;GAxLwB;KAAA", "debugId": null}}, {"offset": {"line": 2145, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/MyProject/MyApp-OrderList/frontend/src/components/materials/MaterialList.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from \"@/components/ui/table\";\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from \"@/components/ui/dialog\";\nimport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n} from \"@/components/ui/card\";\nimport {\n  materialApi,\n  priceApi,\n  Material,\n  MaterialPrice,\n  PaginationResponse,\n} from \"@/lib/api\";\nimport {\n  Pencil,\n  Trash2,\n  Plus,\n  Search,\n  Package,\n  DollarSign,\n} from \"lucide-react\";\nimport { toast } from \"sonner\";\nimport MaterialForm from \"./MaterialForm\";\n\ninterface MaterialListProps {\n  onMaterialSelect?: (material: Material) => void;\n  selectable?: boolean;\n}\n\nexport default function MaterialList({\n  onMaterialSelect,\n  selectable = false,\n}: MaterialListProps) {\n  const [materials, setMaterials] = useState<Material[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [pagination, setPagination] = useState<PaginationResponse>({\n    page: 1,\n    limit: 10,\n    total: 0,\n    pages: 0,\n  });\n  const [deleteDialog, setDeleteDialog] = useState<{\n    open: boolean;\n    material: Material | null;\n  }>({\n    open: false,\n    material: null,\n  });\n\n  const [materialForm, setMaterialForm] = useState<{\n    open: boolean;\n    material: Material | null;\n  }>({\n    open: false,\n    material: null,\n  });\n\n  // 加载物料列表\n  const loadMaterials = async (page = 1, search = \"\") => {\n    try {\n      setLoading(true);\n      const response = search\n        ? await materialApi.searchMaterials(search, {\n            page,\n            limit: pagination.limit,\n          })\n        : await materialApi.getMaterials({ page, limit: pagination.limit });\n\n      if (response.success && response.data) {\n        // 获取每个物料的最新价格\n        const materialsWithPrices = await Promise.all(\n          response.data.materials.map(async (material) => {\n            try {\n              const priceResponse = await priceApi.getCurrentPrice(material.id);\n              return {\n                ...material,\n                currentPrice: priceResponse.success ? priceResponse.data : null,\n              };\n            } catch (error) {\n              return {\n                ...material,\n                currentPrice: null,\n              };\n            }\n          })\n        );\n\n        setMaterials(materialsWithPrices);\n        setPagination(response.data.pagination);\n      }\n    } catch (error) {\n      console.error(\"Failed to load materials:\", error);\n      toast.error(\"加载物料列表失败\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 初始加载\n  useEffect(() => {\n    loadMaterials();\n  }, []);\n\n  // 搜索处理\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    loadMaterials(1, searchQuery);\n  };\n\n  // 新增物料\n  const handleAdd = () => {\n    setMaterialForm({ open: true, material: null });\n  };\n\n  // 编辑物料\n  const handleEdit = (material: Material) => {\n    setMaterialForm({ open: true, material });\n  };\n\n  // 删除物料\n  const handleDelete = async () => {\n    if (!deleteDialog.material) return;\n\n    try {\n      const response = await materialApi.deleteMaterial(\n        deleteDialog.material.id\n      );\n      if (response.success) {\n        toast.success(\"物料删除成功\");\n        loadMaterials(pagination.page, searchQuery);\n        setDeleteDialog({ open: false, material: null });\n      }\n    } catch (error: any) {\n      console.error(\"Failed to delete material:\", error);\n      const errorMessage = error.response?.data?.message || \"删除物料失败\";\n      toast.error(errorMessage);\n    }\n  };\n\n  // 表单成功回调\n  const handleFormSuccess = () => {\n    loadMaterials(pagination.page, searchQuery);\n  };\n\n  // 分页处理\n  const handlePageChange = (newPage: number) => {\n    loadMaterials(newPage, searchQuery);\n  };\n\n  // 格式化日期\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString(\"zh-CN\");\n  };\n\n  // 格式化价格\n  const formatPrice = (price: MaterialPrice | null) => {\n    if (!price) return \"未设置\";\n    const unitPrice =\n      typeof price.unitPrice === \"string\"\n        ? parseFloat(price.unitPrice)\n        : price.unitPrice;\n    return `${price.currency} ${unitPrice.toFixed(2)}`;\n  };\n\n  return (\n    <Card>\n      <CardHeader>\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Package className=\"h-5 w-5\" />\n              物料管理\n            </CardTitle>\n            <CardDescription>管理物料信息和价格</CardDescription>\n          </div>\n          <Button onClick={handleAdd}>\n            <Plus className=\"h-4 w-4 mr-2\" />\n            新增物料\n          </Button>\n        </div>\n      </CardHeader>\n      <CardContent>\n        {/* 搜索栏 */}\n        <form onSubmit={handleSearch} className=\"flex gap-2 mb-4\">\n          <div className=\"relative flex-1\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n            <Input\n              placeholder=\"搜索物料名称...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"pl-10\"\n            />\n          </div>\n          <Button type=\"submit\" variant=\"outline\">\n            搜索\n          </Button>\n        </form>\n\n        {/* 物料表格 */}\n        <div className=\"border rounded-lg\">\n          <Table>\n            <TableHeader>\n              <TableRow>\n                <TableHead>物料名称</TableHead>\n                <TableHead>基本单位</TableHead>\n                <TableHead>当前价格</TableHead>\n                <TableHead>创建时间</TableHead>\n                <TableHead className=\"text-right\">操作</TableHead>\n              </TableRow>\n            </TableHeader>\n            <TableBody>\n              {loading ? (\n                <TableRow>\n                  <TableCell colSpan={5} className=\"text-center py-8\">\n                    加载中...\n                  </TableCell>\n                </TableRow>\n              ) : materials.length === 0 ? (\n                <TableRow>\n                  <TableCell\n                    colSpan={5}\n                    className=\"text-center py-8 text-gray-500\"\n                  >\n                    {searchQuery ? \"未找到匹配的物料\" : \"暂无物料数据\"}\n                  </TableCell>\n                </TableRow>\n              ) : (\n                materials.map((material) => (\n                  <TableRow\n                    key={material.id}\n                    className={\n                      selectable ? \"cursor-pointer hover:bg-gray-50\" : \"\"\n                    }\n                    onClick={\n                      selectable\n                        ? () => onMaterialSelect?.(material)\n                        : undefined\n                    }\n                  >\n                    <TableCell className=\"font-medium\">\n                      {material.materialName}\n                    </TableCell>\n                    <TableCell>{material.baseUnit}</TableCell>\n                    <TableCell>\n                      <div className=\"flex items-center gap-1\">\n                        <DollarSign className=\"h-4 w-4 text-green-600\" />\n                        {formatPrice((material as any).currentPrice)}\n                      </div>\n                    </TableCell>\n                    <TableCell>{formatDate(material.createdAt)}</TableCell>\n                    <TableCell className=\"text-right\">\n                      <div className=\"flex justify-end gap-2\">\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => handleEdit(material)}\n                        >\n                          <Pencil className=\"h-4 w-4\" />\n                        </Button>\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={(e) => {\n                            e.stopPropagation();\n                            setDeleteDialog({ open: true, material });\n                          }}\n                        >\n                          <Trash2 className=\"h-4 w-4\" />\n                        </Button>\n                      </div>\n                    </TableCell>\n                  </TableRow>\n                ))\n              )}\n            </TableBody>\n          </Table>\n        </div>\n\n        {/* 分页 */}\n        {pagination.pages > 1 && (\n          <div className=\"flex items-center justify-between mt-4\">\n            <div className=\"text-sm text-gray-500\">\n              共 {pagination.total} 条记录，第 {pagination.page} /{\" \"}\n              {pagination.pages} 页\n            </div>\n            <div className=\"flex gap-2\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                disabled={pagination.page <= 1}\n                onClick={() => handlePageChange(pagination.page - 1)}\n              >\n                上一页\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                disabled={pagination.page >= pagination.pages}\n                onClick={() => handlePageChange(pagination.page + 1)}\n              >\n                下一页\n              </Button>\n            </div>\n          </div>\n        )}\n      </CardContent>\n\n      {/* 删除确认对话框 */}\n      <Dialog\n        open={deleteDialog.open}\n        onOpenChange={(open) => setDeleteDialog({ open, material: null })}\n      >\n        <DialogContent>\n          <DialogHeader>\n            <DialogTitle>确认删除</DialogTitle>\n            <DialogDescription>\n              确定要删除物料 &quot;{deleteDialog.material?.materialName}&quot;\n              吗？此操作不可撤销。\n            </DialogDescription>\n          </DialogHeader>\n          <DialogFooter>\n            <Button\n              variant=\"outline\"\n              onClick={() => setDeleteDialog({ open: false, material: null })}\n            >\n              取消\n            </Button>\n            <Button variant=\"destructive\" onClick={handleDelete}>\n              删除\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* 物料表单对话框 */}\n      <MaterialForm\n        open={materialForm.open}\n        onOpenChange={(open) => setMaterialForm({ ...materialForm, open })}\n        material={materialForm.material}\n        onSuccess={handleFormSuccess}\n      />\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAQA;AAQA;AAOA;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;;;AA5CA;;;;;;;;;;;AAmDe,SAAS,aAAa,EACnC,gBAAgB,EAChB,aAAa,KAAK,EACA;;IAClB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;QAC/D,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAG5C;QACD,MAAM;QACN,UAAU;IACZ;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAG5C;QACD,MAAM;QACN,UAAU;IACZ;IAEA,SAAS;IACT,MAAM,gBAAgB,OAAO,OAAO,CAAC,EAAE,SAAS,EAAE;QAChD,IAAI;YACF,WAAW;YACX,MAAM,WAAW,SACb,MAAM,oHAAA,CAAA,cAAW,CAAC,eAAe,CAAC,QAAQ;gBACxC;gBACA,OAAO,WAAW,KAAK;YACzB,KACA,MAAM,oHAAA,CAAA,cAAW,CAAC,YAAY,CAAC;gBAAE;gBAAM,OAAO,WAAW,KAAK;YAAC;YAEnE,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,cAAc;gBACd,MAAM,sBAAsB,MAAM,QAAQ,GAAG,CAC3C,SAAS,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO;oBACjC,IAAI;wBACF,MAAM,gBAAgB,MAAM,oHAAA,CAAA,WAAQ,CAAC,eAAe,CAAC,SAAS,EAAE;wBAChE,OAAO;4BACL,GAAG,QAAQ;4BACX,cAAc,cAAc,OAAO,GAAG,cAAc,IAAI,GAAG;wBAC7D;oBACF,EAAE,OAAO,OAAO;wBACd,OAAO;4BACL,GAAG,QAAQ;4BACX,cAAc;wBAChB;oBACF;gBACF;gBAGF,aAAa;gBACb,cAAc,SAAS,IAAI,CAAC,UAAU;YACxC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,OAAO;IACP,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG,EAAE;IAEL,OAAO;IACP,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,cAAc,GAAG;IACnB;IAEA,OAAO;IACP,MAAM,YAAY;QAChB,gBAAgB;YAAE,MAAM;YAAM,UAAU;QAAK;IAC/C;IAEA,OAAO;IACP,MAAM,aAAa,CAAC;QAClB,gBAAgB;YAAE,MAAM;YAAM;QAAS;IACzC;IAEA,OAAO;IACP,MAAM,eAAe;QACnB,IAAI,CAAC,aAAa,QAAQ,EAAE;QAE5B,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,cAAW,CAAC,cAAc,CAC/C,aAAa,QAAQ,CAAC,EAAE;YAE1B,IAAI,SAAS,OAAO,EAAE;gBACpB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,cAAc,WAAW,IAAI,EAAE;gBAC/B,gBAAgB;oBAAE,MAAM;oBAAO,UAAU;gBAAK;YAChD;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WAAW;YACtD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,SAAS;IACT,MAAM,oBAAoB;QACxB,cAAc,WAAW,IAAI,EAAE;IACjC;IAEA,OAAO;IACP,MAAM,mBAAmB,CAAC;QACxB,cAAc,SAAS;IACzB;IAEA,QAAQ;IACR,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC;IACjD;IAEA,QAAQ;IACR,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,OAAO,OAAO;QACnB,MAAM,YACJ,OAAO,MAAM,SAAS,KAAK,WACvB,WAAW,MAAM,SAAS,IAC1B,MAAM,SAAS;QACrB,OAAO,GAAG,MAAM,QAAQ,CAAC,CAAC,EAAE,UAAU,OAAO,CAAC,IAAI;IACpD;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;;0BACH,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,2MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGjC,6LAAC,mIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAEnB,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS;;8CACf,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;0BAKvC,6LAAC,mIAAA,CAAA,cAAW;;kCAEV,6LAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC,oIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;0CAGd,6LAAC,qIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAS,SAAQ;0CAAU;;;;;;;;;;;;kCAM1C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;;8CACJ,6LAAC,oIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;0DACP,6LAAC,oIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,oIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,oIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,oIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,oIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAa;;;;;;;;;;;;;;;;;8CAGtC,6LAAC,oIAAA,CAAA,YAAS;8CACP,wBACC,6LAAC,oIAAA,CAAA,WAAQ;kDACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;4CAAC,SAAS;4CAAG,WAAU;sDAAmB;;;;;;;;;;+CAIpD,UAAU,MAAM,KAAK,kBACvB,6LAAC,oIAAA,CAAA,WAAQ;kDACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;4CACR,SAAS;4CACT,WAAU;sDAET,cAAc,aAAa;;;;;;;;;;+CAIhC,UAAU,GAAG,CAAC,CAAC,yBACb,6LAAC,oIAAA,CAAA,WAAQ;4CAEP,WACE,aAAa,oCAAoC;4CAEnD,SACE,aACI,IAAM,mBAAmB,YACzB;;8DAGN,6LAAC,oIAAA,CAAA,YAAS;oDAAC,WAAU;8DAClB,SAAS,YAAY;;;;;;8DAExB,6LAAC,oIAAA,CAAA,YAAS;8DAAE,SAAS,QAAQ;;;;;;8DAC7B,6LAAC,oIAAA,CAAA,YAAS;8DACR,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;4DACrB,YAAY,AAAC,SAAiB,YAAY;;;;;;;;;;;;8DAG/C,6LAAC,oIAAA,CAAA,YAAS;8DAAE,WAAW,SAAS,SAAS;;;;;;8DACzC,6LAAC,oIAAA,CAAA,YAAS;oDAAC,WAAU;8DACnB,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,WAAW;0EAE1B,cAAA,6LAAC,yMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;0EAEpB,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,CAAC;oEACR,EAAE,eAAe;oEACjB,gBAAgB;wEAAE,MAAM;wEAAM;oEAAS;gEACzC;0EAEA,cAAA,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2CAtCnB,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;oBAkD3B,WAAW,KAAK,GAAG,mBAClB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCAAwB;oCAClC,WAAW,KAAK;oCAAC;oCAAQ,WAAW,IAAI;oCAAC;oCAAG;oCAC9C,WAAW,KAAK;oCAAC;;;;;;;0CAEpB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,UAAU,WAAW,IAAI,IAAI;wCAC7B,SAAS,IAAM,iBAAiB,WAAW,IAAI,GAAG;kDACnD;;;;;;kDAGD,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,UAAU,WAAW,IAAI,IAAI,WAAW,KAAK;wCAC7C,SAAS,IAAM,iBAAiB,WAAW,IAAI,GAAG;kDACnD;;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC,qIAAA,CAAA,SAAM;gBACL,MAAM,aAAa,IAAI;gBACvB,cAAc,CAAC,OAAS,gBAAgB;wBAAE;wBAAM,UAAU;oBAAK;0BAE/D,cAAA,6LAAC,qIAAA,CAAA,gBAAa;;sCACZ,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,6LAAC,qIAAA,CAAA,oBAAiB;;wCAAC;wCACF,aAAa,QAAQ,EAAE;wCAAa;;;;;;;;;;;;;sCAIvD,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,gBAAgB;4CAAE,MAAM;4CAAO,UAAU;wCAAK;8CAC9D;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAc,SAAS;8CAAc;;;;;;;;;;;;;;;;;;;;;;;0BAQ3D,6LAAC,kJAAA,CAAA,UAAY;gBACX,MAAM,aAAa,IAAI;gBACvB,cAAc,CAAC,OAAS,gBAAgB;wBAAE,GAAG,YAAY;wBAAE;oBAAK;gBAChE,UAAU,aAAa,QAAQ;gBAC/B,WAAW;;;;;;;;;;;;AAInB;GA3TwB;KAAA", "debugId": null}}, {"offset": {"line": 2786, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/MyProject/MyApp-OrderList/frontend/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 3035, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/MyProject/MyApp-OrderList/frontend/src/components/orders/OrderForm.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from \"@/components/ui/dialog\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from \"@/components/ui/table\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport {\n  orderApi,\n  customerApi,\n  materialApi,\n  priceApi,\n  SalesOrder,\n  Customer,\n  Material,\n  CreateSalesOrderRequest,\n} from \"@/lib/api\";\nimport { Plus, Trash2 } from \"lucide-react\";\nimport { toast } from \"sonner\";\n\ninterface OrderFormProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n  order?: SalesOrder | null;\n  onSuccess: () => void;\n}\n\ninterface OrderDetail {\n  materialId: string;\n  materialName: string;\n  quantity: number;\n  unit: string;\n  unitPrice: number;\n  totalAmount: number;\n}\n\ninterface FormData {\n  customerId: string;\n  customerPoNumber: string;\n  orderDetails: OrderDetail[];\n}\n\nexport default function OrderForm({\n  open,\n  onOpenChange,\n  order,\n  onSuccess,\n}: OrderFormProps) {\n  const [loading, setLoading] = useState(false);\n  const [customers, setCustomers] = useState<Customer[]>([]);\n  const [materials, setMaterials] = useState<Material[]>([]);\n  const [formData, setFormData] = useState<FormData>({\n    customerId: \"\",\n    customerPoNumber: \"\",\n    orderDetails: [\n      {\n        materialId: \"\",\n        materialName: \"\",\n        quantity: 0,\n        unit: \"\",\n        unitPrice: 0,\n        totalAmount: 0,\n      },\n    ],\n  });\n\n  // 加载客户和物料数据\n  useEffect(() => {\n    if (open) {\n      loadCustomers();\n      loadMaterials();\n    }\n  }, [open]);\n\n  // 初始化表单数据\n  useEffect(() => {\n    if (order) {\n      setFormData({\n        customerId: order.customerId,\n        customerPoNumber: order.customerPoNumber || \"\",\n        orderDetails:\n          order.salesOrderDetails?.map((detail) => ({\n            materialId: detail.materialId,\n            materialName: detail.material?.materialName || \"\",\n            quantity:\n              typeof detail.quantity === \"string\"\n                ? parseFloat(detail.quantity)\n                : detail.quantity,\n            unit: detail.unit,\n            unitPrice:\n              typeof detail.unitPrice === \"string\"\n                ? parseFloat(detail.unitPrice)\n                : detail.unitPrice,\n            totalAmount:\n              typeof detail.totalAmount === \"string\"\n                ? parseFloat(detail.totalAmount)\n                : detail.totalAmount,\n          })) || [],\n      });\n    } else {\n      setFormData({\n        customerId: \"\",\n        customerPoNumber: \"\",\n        orderDetails: [\n          {\n            materialId: \"\",\n            materialName: \"\",\n            quantity: 1,\n            unit: \"\",\n            unitPrice: 0,\n            totalAmount: 0,\n          },\n        ],\n      });\n    }\n  }, [order]);\n\n  const loadCustomers = async () => {\n    try {\n      const response = await customerApi.getCustomers({ limit: 100 });\n      if (response.success && response.data) {\n        setCustomers(response.data.customers);\n      }\n    } catch (error) {\n      console.error(\"Failed to load customers:\", error);\n      toast.error(\"加载客户列表失败\");\n    }\n  };\n\n  const loadMaterials = async () => {\n    try {\n      const response = await materialApi.getMaterials({ limit: 100 });\n      if (response.success && response.data) {\n        setMaterials(response.data.materials);\n      }\n    } catch (error) {\n      console.error(\"Failed to load materials:\", error);\n      toast.error(\"加载物料列表失败\");\n    }\n  };\n\n  const handleInputChange = (field: keyof FormData, value: string) => {\n    setFormData((prev) => ({\n      ...prev,\n      [field]: value,\n    }));\n  };\n\n  const addOrderDetail = () => {\n    setFormData((prev) => ({\n      ...prev,\n      orderDetails: [\n        ...prev.orderDetails,\n        {\n          materialId: \"\",\n          materialName: \"\",\n          quantity: 0,\n          unit: \"\",\n          unitPrice: 0,\n          totalAmount: 0,\n        },\n      ],\n    }));\n  };\n\n  const removeOrderDetail = (index: number) => {\n    if (formData.orderDetails.length > 1) {\n      setFormData((prev) => ({\n        ...prev,\n        orderDetails: prev.orderDetails.filter((_, i) => i !== index),\n      }));\n    }\n  };\n\n  const updateOrderDetail = async (\n    index: number,\n    field: keyof OrderDetail,\n    value: string | number\n  ) => {\n    setFormData((prev) => {\n      const newDetails = [...prev.orderDetails];\n      const detail = { ...newDetails[index] };\n\n      if (field === \"materialId\") {\n        const material = materials.find((m) => m.id === value);\n        if (material) {\n          detail.materialId = value as string;\n          detail.materialName = material.materialName;\n          detail.unit = material.baseUnit;\n\n          // 异步获取物料当前价格\n          priceApi\n            .getCurrentPrice(value as string)\n            .then((response) => {\n              if (response.success && response.data) {\n                setFormData((currentData) => {\n                  const currentDetails = [...currentData.orderDetails];\n                  const currentDetail = { ...currentDetails[index] };\n                  currentDetail.unitPrice =\n                    typeof response.data.unitPrice === \"string\"\n                      ? parseFloat(response.data.unitPrice)\n                      : response.data.unitPrice;\n                  // 只有在有数量和价格时才计算金额\n                  if (\n                    currentDetail.quantity > 0 &&\n                    currentDetail.unitPrice > 0\n                  ) {\n                    currentDetail.totalAmount =\n                      currentDetail.quantity * currentDetail.unitPrice;\n                  } else {\n                    currentDetail.totalAmount = 0;\n                  }\n                  currentDetails[index] = currentDetail;\n                  return {\n                    ...currentData,\n                    orderDetails: currentDetails,\n                  };\n                });\n              }\n            })\n            .catch((error) => {\n              console.warn(\"Failed to load material price:\", error);\n              // 价格获取失败时不显示错误，让用户手动输入价格\n            });\n        }\n      } else {\n        (detail as any)[field] = value;\n      }\n\n      // 重新计算总金额 - 只有在有数量和价格时才计算\n      if (field === \"quantity\" || field === \"unitPrice\") {\n        if (detail.quantity > 0 && detail.unitPrice > 0) {\n          detail.totalAmount = detail.quantity * detail.unitPrice;\n        } else {\n          detail.totalAmount = 0;\n        }\n      }\n\n      newDetails[index] = detail;\n      return {\n        ...prev,\n        orderDetails: newDetails,\n      };\n    });\n  };\n\n  const calculateTotalAmount = () => {\n    return formData.orderDetails.reduce((sum, detail) => {\n      // 只计算有数量和价格的明细行\n      if (detail.quantity > 0 && detail.unitPrice > 0) {\n        return sum + detail.totalAmount;\n      }\n      return sum;\n    }, 0);\n  };\n\n  // 安全的数字格式化函数\n  const formatNumber = (value: number | string) => {\n    const numValue = typeof value === \"string\" ? parseFloat(value) : value;\n    return (numValue || 0).toFixed(2);\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!formData.customerId) {\n      toast.error(\"请选择客户\");\n      return;\n    }\n\n    if (formData.orderDetails.length === 0) {\n      toast.error(\"请添加至少一个订单明细\");\n      return;\n    }\n\n    // 验证订单明细\n    for (const detail of formData.orderDetails) {\n      if (!detail.materialId) {\n        toast.error(\"请为所有明细选择物料\");\n        return;\n      }\n      if (detail.quantity <= 0) {\n        toast.error(\"数量必须大于0\");\n        return;\n      }\n      if (detail.unitPrice <= 0) {\n        toast.error(\"单价必须大于0\");\n        return;\n      }\n    }\n\n    try {\n      setLoading(true);\n\n      const requestData: CreateSalesOrderRequest = {\n        customerId: formData.customerId,\n        customerPoNumber: formData.customerPoNumber || undefined,\n        orderDetails: formData.orderDetails.map((detail) => ({\n          materialId: detail.materialId,\n          quantity: detail.quantity,\n          unit: detail.unit,\n          unitPrice: detail.unitPrice,\n        })),\n      };\n\n      if (order) {\n        // 编辑订单\n        const response = await orderApi.updateOrder(order.id, requestData);\n\n        if (response.success) {\n          toast.success(\"订单更新成功\");\n          onSuccess();\n          onOpenChange(false);\n        }\n      } else {\n        // 新增订单\n        const response = await orderApi.createOrder(requestData);\n\n        if (response.success) {\n          toast.success(\"订单创建成功\");\n          onSuccess();\n          onOpenChange(false);\n        }\n      }\n    } catch (error: any) {\n      console.error(\"Failed to save order:\", error);\n      const errorMessage = error.response?.data?.message || \"保存订单失败\";\n      toast.error(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"!max-w-[95vw] !w-[95vw] max-h-[95vh] h-auto overflow-y-auto p-8 sm:!max-w-[95vw]\">\n        <DialogHeader>\n          <DialogTitle>{order ? \"编辑订单\" : \"新增订单\"}</DialogTitle>\n          <DialogDescription>\n            {order ? \"修改订单信息和明细\" : \"创建新的销售订单\"}\n          </DialogDescription>\n        </DialogHeader>\n\n        <form onSubmit={handleSubmit} className=\"space-y-8 w-full\">\n          {/* 基本信息 */}\n          <Card className=\"w-full\">\n            <CardHeader>\n              <CardTitle className=\"text-lg\">基本信息</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"customerId\">客户 *</Label>\n                  <Select\n                    value={formData.customerId}\n                    onValueChange={(value) =>\n                      handleInputChange(\"customerId\", value)\n                    }\n                  >\n                    <SelectTrigger>\n                      <SelectValue placeholder=\"选择客户\" />\n                    </SelectTrigger>\n                    <SelectContent>\n                      {customers.map((customer) => (\n                        <SelectItem key={customer.id} value={customer.id}>\n                          {customer.customerName}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                </div>\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"customerPoNumber\">客户订单号</Label>\n                  <Input\n                    id=\"customerPoNumber\"\n                    value={formData.customerPoNumber}\n                    onChange={(e) =>\n                      handleInputChange(\"customerPoNumber\", e.target.value)\n                    }\n                    placeholder=\"输入客户订单号\"\n                  />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* 订单明细 */}\n          <Card className=\"w-full\">\n            <CardHeader>\n              <div className=\"flex items-center justify-between\">\n                <CardTitle className=\"text-lg\">订单明细</CardTitle>\n                <Button type=\"button\" onClick={addOrderDetail} size=\"sm\">\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  添加明细\n                </Button>\n              </div>\n            </CardHeader>\n            <CardContent className=\"w-full overflow-x-auto\">\n              {formData.orderDetails.length === 0 ? (\n                <div className=\"text-center py-8 text-gray-500\">\n                  暂无订单明细，请点击\"添加明细\"按钮添加\n                </div>\n              ) : (\n                <div className=\"border rounded-lg overflow-x-auto w-full\">\n                  <Table className=\"w-full table-auto\">\n                    <TableHeader>\n                      <TableRow>\n                        <TableHead className=\"min-w-[400px] max-w-[600px]\">\n                          物料名称\n                        </TableHead>\n                        <TableHead className=\"w-[220px]\">数量</TableHead>\n                        <TableHead className=\"w-[150px]\">单位</TableHead>\n                        <TableHead className=\"w-[180px]\">单价(¥)</TableHead>\n                        <TableHead className=\"w-[180px]\">金额(¥)</TableHead>\n                        <TableHead className=\"w-[120px] text-right\">\n                          操作\n                        </TableHead>\n                      </TableRow>\n                    </TableHeader>\n                    <TableBody>\n                      {formData.orderDetails.map((detail, index) => (\n                        <TableRow key={index}>\n                          <TableCell>\n                            <Select\n                              value={detail.materialId}\n                              onValueChange={(value) =>\n                                updateOrderDetail(index, \"materialId\", value)\n                              }\n                            >\n                              <SelectTrigger className=\"w-full min-w-[380px]\">\n                                <SelectValue placeholder=\"选择物料\" />\n                              </SelectTrigger>\n                              <SelectContent>\n                                {materials.map((material) => (\n                                  <SelectItem\n                                    key={material.id}\n                                    value={material.id}\n                                  >\n                                    {material.materialName}\n                                  </SelectItem>\n                                ))}\n                              </SelectContent>\n                            </Select>\n                          </TableCell>\n                          <TableCell>\n                            <Input\n                              type=\"text\"\n                              value={\n                                detail.quantity > 0\n                                  ? detail.quantity.toString()\n                                  : \"\"\n                              }\n                              onChange={(e) => {\n                                const value = e.target.value;\n                                // 只允许输入数字和小数点\n                                if (value === \"\" || /^\\d*\\.?\\d*$/.test(value)) {\n                                  updateOrderDetail(\n                                    index,\n                                    \"quantity\",\n                                    value === \"\" ? 0 : parseFloat(value) || 0\n                                  );\n                                }\n                              }}\n                              placeholder=\"输入数量\"\n                              className=\"w-full text-right min-w-[180px] px-3 py-2 text-base\"\n                              style={{\n                                width: `${Math.max(\n                                  180,\n                                  (detail.quantity > 0\n                                    ? detail.quantity.toString().length\n                                    : 4) *\n                                    12 +\n                                    24\n                                )}px`,\n                              }}\n                            />\n                          </TableCell>\n                          <TableCell>\n                            <div className=\"px-3 py-2 text-center\">\n                              {detail.unit || \"-\"}\n                            </div>\n                          </TableCell>\n                          <TableCell>\n                            <div className=\"px-3 py-2 text-right font-medium\">\n                              {detail.unitPrice > 0\n                                ? formatNumber(detail.unitPrice)\n                                : \"-\"}\n                            </div>\n                          </TableCell>\n                          <TableCell>\n                            <div className=\"px-3 py-2 text-right font-medium\">\n                              {detail.quantity > 0 && detail.unitPrice > 0\n                                ? formatNumber(detail.totalAmount)\n                                : \"-\"}\n                            </div>\n                          </TableCell>\n                          <TableCell className=\"text-right\">\n                            <Button\n                              type=\"button\"\n                              variant=\"outline\"\n                              size=\"sm\"\n                              onClick={() => removeOrderDetail(index)}\n                              disabled={formData.orderDetails.length <= 1}\n                            >\n                              <Trash2 className=\"h-4 w-4\" />\n                            </Button>\n                          </TableCell>\n                        </TableRow>\n                      ))}\n                    </TableBody>\n                  </Table>\n                </div>\n              )}\n\n              {/* 总计 */}\n              {formData.orderDetails.length > 0 && (\n                <div className=\"flex justify-end mt-4 p-4 bg-gray-50 rounded-lg\">\n                  <div className=\"text-lg font-semibold text-blue-600\">\n                    订单总金额: ¥{formatNumber(calculateTotalAmount())}\n                  </div>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n\n          <DialogFooter className=\"flex justify-end space-x-4 pt-6 border-t\">\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={() => onOpenChange(false)}\n              size=\"lg\"\n            >\n              取消\n            </Button>\n            <Button type=\"submit\" disabled={loading} size=\"lg\">\n              {loading ? \"保存中...\" : order ? \"更新订单\" : \"创建订单\"}\n            </Button>\n          </DialogFooter>\n        </form>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAQA;AAOA;AAQA;AACA;AAUA;AAAA;AACA;;;AAzCA;;;;;;;;;;;;AAiEe,SAAS,UAAU,EAChC,IAAI,EACJ,YAAY,EACZ,KAAK,EACL,SAAS,EACM;;IACf,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,YAAY;QACZ,kBAAkB;QAClB,cAAc;YACZ;gBACE,YAAY;gBACZ,cAAc;gBACd,UAAU;gBACV,MAAM;gBACN,WAAW;gBACX,aAAa;YACf;SACD;IACH;IAEA,YAAY;IACZ,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,MAAM;gBACR;gBACA;YACF;QACF;8BAAG;QAAC;KAAK;IAET,UAAU;IACV,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,OAAO;gBACT,YAAY;oBACV,YAAY,MAAM,UAAU;oBAC5B,kBAAkB,MAAM,gBAAgB,IAAI;oBAC5C,cACE,MAAM,iBAAiB,EAAE;+CAAI,CAAC,SAAW,CAAC;gCACxC,YAAY,OAAO,UAAU;gCAC7B,cAAc,OAAO,QAAQ,EAAE,gBAAgB;gCAC/C,UACE,OAAO,OAAO,QAAQ,KAAK,WACvB,WAAW,OAAO,QAAQ,IAC1B,OAAO,QAAQ;gCACrB,MAAM,OAAO,IAAI;gCACjB,WACE,OAAO,OAAO,SAAS,KAAK,WACxB,WAAW,OAAO,SAAS,IAC3B,OAAO,SAAS;gCACtB,aACE,OAAO,OAAO,WAAW,KAAK,WAC1B,WAAW,OAAO,WAAW,IAC7B,OAAO,WAAW;4BAC1B,CAAC;iDAAM,EAAE;gBACb;YACF,OAAO;gBACL,YAAY;oBACV,YAAY;oBACZ,kBAAkB;oBAClB,cAAc;wBACZ;4BACE,YAAY;4BACZ,cAAc;4BACd,UAAU;4BACV,MAAM;4BACN,WAAW;4BACX,aAAa;wBACf;qBACD;gBACH;YACF;QACF;8BAAG;QAAC;KAAM;IAEV,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,cAAW,CAAC,YAAY,CAAC;gBAAE,OAAO;YAAI;YAC7D,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,aAAa,SAAS,IAAI,CAAC,SAAS;YACtC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,cAAW,CAAC,YAAY,CAAC;gBAAE,OAAO;YAAI;YAC7D,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,aAAa,SAAS,IAAI,CAAC,SAAS;YACtC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,oBAAoB,CAAC,OAAuB;QAChD,YAAY,CAAC,OAAS,CAAC;gBACrB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,iBAAiB;QACrB,YAAY,CAAC,OAAS,CAAC;gBACrB,GAAG,IAAI;gBACP,cAAc;uBACT,KAAK,YAAY;oBACpB;wBACE,YAAY;wBACZ,cAAc;wBACd,UAAU;wBACV,MAAM;wBACN,WAAW;wBACX,aAAa;oBACf;iBACD;YACH,CAAC;IACH;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,SAAS,YAAY,CAAC,MAAM,GAAG,GAAG;YACpC,YAAY,CAAC,OAAS,CAAC;oBACrB,GAAG,IAAI;oBACP,cAAc,KAAK,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;gBACzD,CAAC;QACH;IACF;IAEA,MAAM,oBAAoB,OACxB,OACA,OACA;QAEA,YAAY,CAAC;YACX,MAAM,aAAa;mBAAI,KAAK,YAAY;aAAC;YACzC,MAAM,SAAS;gBAAE,GAAG,UAAU,CAAC,MAAM;YAAC;YAEtC,IAAI,UAAU,cAAc;gBAC1B,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;gBAChD,IAAI,UAAU;oBACZ,OAAO,UAAU,GAAG;oBACpB,OAAO,YAAY,GAAG,SAAS,YAAY;oBAC3C,OAAO,IAAI,GAAG,SAAS,QAAQ;oBAE/B,aAAa;oBACb,oHAAA,CAAA,WAAQ,CACL,eAAe,CAAC,OAChB,IAAI,CAAC,CAAC;wBACL,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;4BACrC,YAAY,CAAC;gCACX,MAAM,iBAAiB;uCAAI,YAAY,YAAY;iCAAC;gCACpD,MAAM,gBAAgB;oCAAE,GAAG,cAAc,CAAC,MAAM;gCAAC;gCACjD,cAAc,SAAS,GACrB,OAAO,SAAS,IAAI,CAAC,SAAS,KAAK,WAC/B,WAAW,SAAS,IAAI,CAAC,SAAS,IAClC,SAAS,IAAI,CAAC,SAAS;gCAC7B,kBAAkB;gCAClB,IACE,cAAc,QAAQ,GAAG,KACzB,cAAc,SAAS,GAAG,GAC1B;oCACA,cAAc,WAAW,GACvB,cAAc,QAAQ,GAAG,cAAc,SAAS;gCACpD,OAAO;oCACL,cAAc,WAAW,GAAG;gCAC9B;gCACA,cAAc,CAAC,MAAM,GAAG;gCACxB,OAAO;oCACL,GAAG,WAAW;oCACd,cAAc;gCAChB;4BACF;wBACF;oBACF,GACC,KAAK,CAAC,CAAC;wBACN,QAAQ,IAAI,CAAC,kCAAkC;oBAC/C,yBAAyB;oBAC3B;gBACJ;YACF,OAAO;gBACJ,MAAc,CAAC,MAAM,GAAG;YAC3B;YAEA,0BAA0B;YAC1B,IAAI,UAAU,cAAc,UAAU,aAAa;gBACjD,IAAI,OAAO,QAAQ,GAAG,KAAK,OAAO,SAAS,GAAG,GAAG;oBAC/C,OAAO,WAAW,GAAG,OAAO,QAAQ,GAAG,OAAO,SAAS;gBACzD,OAAO;oBACL,OAAO,WAAW,GAAG;gBACvB;YACF;YAEA,UAAU,CAAC,MAAM,GAAG;YACpB,OAAO;gBACL,GAAG,IAAI;gBACP,cAAc;YAChB;QACF;IACF;IAEA,MAAM,uBAAuB;QAC3B,OAAO,SAAS,YAAY,CAAC,MAAM,CAAC,CAAC,KAAK;YACxC,gBAAgB;YAChB,IAAI,OAAO,QAAQ,GAAG,KAAK,OAAO,SAAS,GAAG,GAAG;gBAC/C,OAAO,MAAM,OAAO,WAAW;YACjC;YACA,OAAO;QACT,GAAG;IACL;IAEA,aAAa;IACb,MAAM,eAAe,CAAC;QACpB,MAAM,WAAW,OAAO,UAAU,WAAW,WAAW,SAAS;QACjE,OAAO,CAAC,YAAY,CAAC,EAAE,OAAO,CAAC;IACjC;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS,UAAU,EAAE;YACxB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,SAAS,YAAY,CAAC,MAAM,KAAK,GAAG;YACtC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,SAAS;QACT,KAAK,MAAM,UAAU,SAAS,YAAY,CAAE;YAC1C,IAAI,CAAC,OAAO,UAAU,EAAE;gBACtB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YACA,IAAI,OAAO,QAAQ,IAAI,GAAG;gBACxB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YACA,IAAI,OAAO,SAAS,IAAI,GAAG;gBACzB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;QACF;QAEA,IAAI;YACF,WAAW;YAEX,MAAM,cAAuC;gBAC3C,YAAY,SAAS,UAAU;gBAC/B,kBAAkB,SAAS,gBAAgB,IAAI;gBAC/C,cAAc,SAAS,YAAY,CAAC,GAAG,CAAC,CAAC,SAAW,CAAC;wBACnD,YAAY,OAAO,UAAU;wBAC7B,UAAU,OAAO,QAAQ;wBACzB,MAAM,OAAO,IAAI;wBACjB,WAAW,OAAO,SAAS;oBAC7B,CAAC;YACH;YAEA,IAAI,OAAO;gBACT,OAAO;gBACP,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE;gBAEtD,IAAI,SAAS,OAAO,EAAE;oBACpB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBACd;oBACA,aAAa;gBACf;YACF,OAAO;gBACL,OAAO;gBACP,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;gBAE5C,IAAI,SAAS,OAAO,EAAE;oBACpB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBACd;oBACA,aAAa;gBACf;YACF;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WAAW;YACtD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,cAAW;sCAAE,QAAQ,SAAS;;;;;;sCAC/B,6LAAC,qIAAA,CAAA,oBAAiB;sCACf,QAAQ,cAAc;;;;;;;;;;;;8BAI3B,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAU;;;;;;;;;;;8CAEjC,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAa;;;;;;kEAC5B,6LAAC,qIAAA,CAAA,SAAM;wDACL,OAAO,SAAS,UAAU;wDAC1B,eAAe,CAAC,QACd,kBAAkB,cAAc;;0EAGlC,6LAAC,qIAAA,CAAA,gBAAa;0EACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EAE3B,6LAAC,qIAAA,CAAA,gBAAa;0EACX,UAAU,GAAG,CAAC,CAAC,yBACd,6LAAC,qIAAA,CAAA,aAAU;wEAAmB,OAAO,SAAS,EAAE;kFAC7C,SAAS,YAAY;uEADP,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;0DAOpC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAmB;;;;;;kEAClC,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,SAAS,gBAAgB;wDAChC,UAAU,CAAC,IACT,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,KAAK;wDAEtD,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQtB,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAU;;;;;;0DAC/B,6LAAC,qIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAS,SAAS;gDAAgB,MAAK;;kEAClD,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;8CAKvC,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;wCACpB,SAAS,YAAY,CAAC,MAAM,KAAK,kBAChC,6LAAC;4CAAI,WAAU;sDAAiC;;;;;iEAIhD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;gDAAC,WAAU;;kEACf,6LAAC,oIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;8EACP,6LAAC,oIAAA,CAAA,YAAS;oEAAC,WAAU;8EAA8B;;;;;;8EAGnD,6LAAC,oIAAA,CAAA,YAAS;oEAAC,WAAU;8EAAY;;;;;;8EACjC,6LAAC,oIAAA,CAAA,YAAS;oEAAC,WAAU;8EAAY;;;;;;8EACjC,6LAAC,oIAAA,CAAA,YAAS;oEAAC,WAAU;8EAAY;;;;;;8EACjC,6LAAC,oIAAA,CAAA,YAAS;oEAAC,WAAU;8EAAY;;;;;;8EACjC,6LAAC,oIAAA,CAAA,YAAS;oEAAC,WAAU;8EAAuB;;;;;;;;;;;;;;;;;kEAKhD,6LAAC,oIAAA,CAAA,YAAS;kEACP,SAAS,YAAY,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAClC,6LAAC,oIAAA,CAAA,WAAQ;;kFACP,6LAAC,oIAAA,CAAA,YAAS;kFACR,cAAA,6LAAC,qIAAA,CAAA,SAAM;4EACL,OAAO,OAAO,UAAU;4EACxB,eAAe,CAAC,QACd,kBAAkB,OAAO,cAAc;;8FAGzC,6LAAC,qIAAA,CAAA,gBAAa;oFAAC,WAAU;8FACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;wFAAC,aAAY;;;;;;;;;;;8FAE3B,6LAAC,qIAAA,CAAA,gBAAa;8FACX,UAAU,GAAG,CAAC,CAAC,yBACd,6LAAC,qIAAA,CAAA,aAAU;4FAET,OAAO,SAAS,EAAE;sGAEjB,SAAS,YAAY;2FAHjB,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;kFAS1B,6LAAC,oIAAA,CAAA,YAAS;kFACR,cAAA,6LAAC,oIAAA,CAAA,QAAK;4EACJ,MAAK;4EACL,OACE,OAAO,QAAQ,GAAG,IACd,OAAO,QAAQ,CAAC,QAAQ,KACxB;4EAEN,UAAU,CAAC;gFACT,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;gFAC5B,cAAc;gFACd,IAAI,UAAU,MAAM,cAAc,IAAI,CAAC,QAAQ;oFAC7C,kBACE,OACA,YACA,UAAU,KAAK,IAAI,WAAW,UAAU;gFAE5C;4EACF;4EACA,aAAY;4EACZ,WAAU;4EACV,OAAO;gFACL,OAAO,GAAG,KAAK,GAAG,CAChB,KACA,CAAC,OAAO,QAAQ,GAAG,IACf,OAAO,QAAQ,CAAC,QAAQ,GAAG,MAAM,GACjC,CAAC,IACH,KACA,IACF,EAAE,CAAC;4EACP;;;;;;;;;;;kFAGJ,6LAAC,oIAAA,CAAA,YAAS;kFACR,cAAA,6LAAC;4EAAI,WAAU;sFACZ,OAAO,IAAI,IAAI;;;;;;;;;;;kFAGpB,6LAAC,oIAAA,CAAA,YAAS;kFACR,cAAA,6LAAC;4EAAI,WAAU;sFACZ,OAAO,SAAS,GAAG,IAChB,aAAa,OAAO,SAAS,IAC7B;;;;;;;;;;;kFAGR,6LAAC,oIAAA,CAAA,YAAS;kFACR,cAAA,6LAAC;4EAAI,WAAU;sFACZ,OAAO,QAAQ,GAAG,KAAK,OAAO,SAAS,GAAG,IACvC,aAAa,OAAO,WAAW,IAC/B;;;;;;;;;;;kFAGR,6LAAC,oIAAA,CAAA,YAAS;wEAAC,WAAU;kFACnB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4EACL,MAAK;4EACL,SAAQ;4EACR,MAAK;4EACL,SAAS,IAAM,kBAAkB;4EACjC,UAAU,SAAS,YAAY,CAAC,MAAM,IAAI;sFAE1C,cAAA,6LAAC,6MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;;;;;;;;;;;;+DAnFT;;;;;;;;;;;;;;;;;;;;;wCA8FxB,SAAS,YAAY,CAAC,MAAM,GAAG,mBAC9B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;oDAAsC;oDAC1C,aAAa;;;;;;;;;;;;;;;;;;;;;;;;sCAOhC,6LAAC,qIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS,IAAM,aAAa;oCAC5B,MAAK;8CACN;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,UAAU;oCAAS,MAAK;8CAC3C,UAAU,WAAW,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrD;GApfwB;KAAA", "debugId": null}}, {"offset": {"line": 3853, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/MyProject/MyApp-OrderList/frontend/src/components/orders/OrderList.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from \"@/components/ui/table\";\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from \"@/components/ui/dialog\";\nimport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n} from \"@/components/ui/card\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { orderApi, SalesOrder, PaginationResponse } from \"@/lib/api\";\nimport { Pencil, Trash2, Plus, Search, ShoppingCart, Eye } from \"lucide-react\";\nimport { toast } from \"sonner\";\nimport OrderForm from \"./OrderForm\";\n\nconst ORDER_STATUS_MAP = {\n  PENDING: { label: \"待确认\", color: \"bg-yellow-100 text-yellow-800\" },\n  CONFIRMED: { label: \"已确认\", color: \"bg-blue-100 text-blue-800\" },\n  SHIPPED: { label: \"已发货\", color: \"bg-purple-100 text-purple-800\" },\n  DELIVERED: { label: \"已交付\", color: \"bg-green-100 text-green-800\" },\n  CANCELLED: { label: \"已取消\", color: \"bg-red-100 text-red-800\" },\n};\n\ninterface OrderListProps {\n  onOrderSelect?: (order: SalesOrder) => void;\n  customerId?: string;\n}\n\nexport default function OrderList({\n  onOrderSelect,\n  customerId,\n}: OrderListProps) {\n  const [orders, setOrders] = useState<SalesOrder[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [statusFilter, setStatusFilter] = useState<string>(\"ALL\");\n  const [pagination, setPagination] = useState<PaginationResponse>({\n    page: 1,\n    limit: 10,\n    total: 0,\n    pages: 0,\n  });\n  const [deleteDialog, setDeleteDialog] = useState<{\n    open: boolean;\n    order: SalesOrder | null;\n  }>({\n    open: false,\n    order: null,\n  });\n  const [orderForm, setOrderForm] = useState<{\n    open: boolean;\n    order: SalesOrder | null;\n  }>({\n    open: false,\n    order: null,\n  });\n\n  // 加载订单列表\n  const loadOrders = async (page = 1, search = \"\", status = \"\") => {\n    try {\n      setLoading(true);\n      const params: any = {\n        page,\n        limit: pagination.limit,\n        ...(status && { status }),\n        ...(customerId && { customerId }),\n      };\n\n      const response = search\n        ? await orderApi.searchOrders(search, params)\n        : await orderApi.getOrders(params);\n\n      if (response.success && response.data) {\n        setOrders(response.data.orders);\n        setPagination(response.data.pagination);\n      }\n    } catch (error) {\n      console.error(\"Failed to load orders:\", error);\n      toast.error(\"加载订单列表失败\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 初始加载\n  useEffect(() => {\n    loadOrders();\n  }, [customerId]);\n\n  // 搜索处理\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    loadOrders(1, searchQuery, statusFilter);\n  };\n\n  // 状态筛选处理\n  const handleStatusFilter = (status: string) => {\n    const filterStatus = status === \"ALL\" ? \"\" : status;\n    setStatusFilter(status);\n    loadOrders(1, searchQuery, filterStatus);\n  };\n\n  // 新增订单\n  const handleAdd = () => {\n    setOrderForm({ open: true, order: null });\n  };\n\n  // 查看订单\n  const handleView = (order: SalesOrder) => {\n    // 如果有外部的onOrderSelect回调，使用它\n    if (onOrderSelect) {\n      onOrderSelect(order);\n    } else {\n      // 否则以只读模式打开表单\n      setOrderForm({ open: true, order });\n    }\n  };\n\n  // 编辑订单\n  const handleEdit = (order: SalesOrder) => {\n    setOrderForm({ open: true, order });\n  };\n\n  // 表单成功回调\n  const handleFormSuccess = () => {\n    loadOrders(pagination.page, searchQuery, statusFilter);\n  };\n\n  // 删除订单\n  const handleDelete = async () => {\n    if (!deleteDialog.order) return;\n\n    try {\n      const response = await orderApi.deleteOrder(deleteDialog.order.id);\n      if (response.success) {\n        toast.success(\"订单删除成功\");\n        loadOrders(pagination.page, searchQuery, statusFilter);\n        setDeleteDialog({ open: false, order: null });\n      }\n    } catch (error: any) {\n      console.error(\"Failed to delete order:\", error);\n      const errorMessage = error.response?.data?.message || \"删除订单失败\";\n      toast.error(errorMessage);\n    }\n  };\n\n  // 分页处理\n  const handlePageChange = (newPage: number) => {\n    loadOrders(newPage, searchQuery, statusFilter);\n  };\n\n  // 格式化日期\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString(\"zh-CN\");\n  };\n\n  // 格式化金额\n  const formatAmount = (amount: number | string) => {\n    const numAmount = typeof amount === \"string\" ? parseFloat(amount) : amount;\n    return `¥${(numAmount || 0).toFixed(2)}`;\n  };\n\n  // 格式化数量\n  const formatNumber = (value: number | string) => {\n    const numValue = typeof value === \"string\" ? parseFloat(value) : value;\n    return (numValue || 0).toFixed(2);\n  };\n\n  // 格式化价格（不带货币符号）\n  const formatPrice = (amount: number | string) => {\n    const numAmount = typeof amount === \"string\" ? parseFloat(amount) : amount;\n    return (numAmount || 0).toFixed(2);\n  };\n\n  return (\n    <Card>\n      <CardHeader>\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <CardTitle className=\"flex items-center gap-2\">\n              <ShoppingCart className=\"h-5 w-5\" />\n              订单管理\n            </CardTitle>\n            <CardDescription>管理销售订单和订单明细</CardDescription>\n          </div>\n          <Button onClick={handleAdd}>\n            <Plus className=\"h-4 w-4 mr-2\" />\n            新增订单\n          </Button>\n        </div>\n      </CardHeader>\n      <CardContent>\n        {/* 搜索和筛选栏 */}\n        <div className=\"flex gap-2 mb-4\">\n          <form onSubmit={handleSearch} className=\"flex gap-2 flex-1\">\n            <div className=\"relative flex-1\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n              <Input\n                placeholder=\"搜索订单号或客户名称...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"pl-10\"\n              />\n            </div>\n            <Button type=\"submit\" variant=\"outline\">\n              搜索\n            </Button>\n          </form>\n          <Select value={statusFilter} onValueChange={handleStatusFilter}>\n            <SelectTrigger className=\"w-32\">\n              <SelectValue placeholder=\"状态筛选\" />\n            </SelectTrigger>\n            <SelectContent>\n              <SelectItem value=\"ALL\">全部状态</SelectItem>\n              {Object.entries(ORDER_STATUS_MAP).map(([status, config]) => (\n                <SelectItem key={status} value={status}>\n                  {config.label}\n                </SelectItem>\n              ))}\n            </SelectContent>\n          </Select>\n        </div>\n\n        {/* 订单表格 */}\n        <div className=\"border rounded-lg\">\n          <Table>\n            <TableHeader>\n              <TableRow>\n                <TableHead>订单ID</TableHead>\n                <TableHead>客户名称</TableHead>\n                <TableHead>客户订单号</TableHead>\n                <TableHead>订单状态</TableHead>\n                <TableHead>物料名称</TableHead>\n                <TableHead className=\"text-right\">数量/单位</TableHead>\n                <TableHead className=\"text-right\">单价(¥)</TableHead>\n                <TableHead className=\"text-right\">明细金额(¥)</TableHead>\n                <TableHead>创建时间</TableHead>\n                <TableHead className=\"text-right\">操作</TableHead>\n              </TableRow>\n            </TableHeader>\n            <TableBody>\n              {loading ? (\n                <TableRow>\n                  <TableCell colSpan={10} className=\"text-center py-8\">\n                    加载中...\n                  </TableCell>\n                </TableRow>\n              ) : orders.length === 0 ? (\n                <TableRow>\n                  <TableCell\n                    colSpan={10}\n                    className=\"text-center py-8 text-gray-500\"\n                  >\n                    {searchQuery || statusFilter\n                      ? \"未找到匹配的订单\"\n                      : \"暂无订单数据\"}\n                  </TableCell>\n                </TableRow>\n              ) : (\n                orders.map((order) => {\n                  const details = order.salesOrderDetails || [];\n                  if (details.length === 0) {\n                    // 如果没有明细，显示一行空明细\n                    return (\n                      <TableRow\n                        key={order.id}\n                        className=\"cursor-pointer hover:bg-gray-50\"\n                        onClick={() => onOrderSelect?.(order)}\n                      >\n                        <TableCell className=\"font-medium font-mono text-sm\">\n                          {order.id.slice(-8)}\n                        </TableCell>\n                        <TableCell>\n                          {order.customer?.customerName || \"未知客户\"}\n                        </TableCell>\n                        <TableCell>{order.customerPoNumber || \"-\"}</TableCell>\n                        <TableCell>\n                          <span\n                            className={`px-2 py-1 rounded-full text-xs font-medium ${\n                              ORDER_STATUS_MAP[order.orderStatus]?.color ||\n                              \"bg-gray-100 text-gray-800\"\n                            }`}\n                          >\n                            {ORDER_STATUS_MAP[order.orderStatus]?.label ||\n                              order.orderStatus}\n                          </span>\n                        </TableCell>\n                        <TableCell className=\"text-gray-500\">-</TableCell>\n                        <TableCell className=\"text-gray-500\">-</TableCell>\n                        <TableCell className=\"text-gray-500\">-</TableCell>\n                        <TableCell className=\"text-gray-500\">-</TableCell>\n                        <TableCell>{formatDate(order.createdAt)}</TableCell>\n                        <TableCell className=\"text-right\">\n                          <div className=\"flex justify-end gap-2\">\n                            <Button\n                              variant=\"outline\"\n                              size=\"sm\"\n                              onClick={(e) => {\n                                e.stopPropagation();\n                                handleView(order);\n                              }}\n                            >\n                              <Eye className=\"h-4 w-4\" />\n                            </Button>\n                            <Button\n                              variant=\"outline\"\n                              size=\"sm\"\n                              onClick={(e) => {\n                                e.stopPropagation();\n                                handleEdit(order);\n                              }}\n                            >\n                              <Pencil className=\"h-4 w-4\" />\n                            </Button>\n                            <Button\n                              variant=\"outline\"\n                              size=\"sm\"\n                              onClick={(e) => {\n                                e.stopPropagation();\n                                setDeleteDialog({ open: true, order });\n                              }}\n                            >\n                              <Trash2 className=\"h-4 w-4\" />\n                            </Button>\n                          </div>\n                        </TableCell>\n                      </TableRow>\n                    );\n                  }\n\n                  // 如果有明细，为每个明细显示一行\n                  return details.map((detail, index) => (\n                    <TableRow\n                      key={`${order.id}-${detail.id}`}\n                      className=\"cursor-pointer hover:bg-gray-50\"\n                      onClick={() => onOrderSelect?.(order)}\n                    >\n                      {/* 只在第一行显示订单基本信息 */}\n                      {index === 0 ? (\n                        <>\n                          <TableCell\n                            className=\"font-medium font-mono text-sm border-r\"\n                            rowSpan={details.length}\n                          >\n                            {order.id.slice(-8)}\n                          </TableCell>\n                          <TableCell\n                            className=\"border-r\"\n                            rowSpan={details.length}\n                          >\n                            {order.customer?.customerName || \"未知客户\"}\n                          </TableCell>\n                          <TableCell\n                            className=\"border-r\"\n                            rowSpan={details.length}\n                          >\n                            {order.customerPoNumber || \"-\"}\n                          </TableCell>\n                          <TableCell\n                            className=\"border-r\"\n                            rowSpan={details.length}\n                          >\n                            <span\n                              className={`px-2 py-1 rounded-full text-xs font-medium ${\n                                ORDER_STATUS_MAP[order.orderStatus]?.color ||\n                                \"bg-gray-100 text-gray-800\"\n                              }`}\n                            >\n                              {ORDER_STATUS_MAP[order.orderStatus]?.label ||\n                                order.orderStatus}\n                            </span>\n                          </TableCell>\n                        </>\n                      ) : null}\n\n                      {/* 明细信息 */}\n                      <TableCell>\n                        {detail.material?.materialName || \"未知物料\"}\n                      </TableCell>\n                      <TableCell className=\"text-right\">\n                        {formatNumber(detail.quantity)} {detail.unit}\n                      </TableCell>\n                      <TableCell className=\"text-right\">\n                        {formatPrice(detail.unitPrice)}\n                      </TableCell>\n                      <TableCell className=\"text-right font-medium\">\n                        {formatPrice(detail.totalAmount)}\n                      </TableCell>\n\n                      {/* 只在第一行显示创建时间和操作按钮 */}\n                      {index === 0 ? (\n                        <>\n                          <TableCell\n                            className=\"border-l\"\n                            rowSpan={details.length}\n                          >\n                            {formatDate(order.createdAt)}\n                          </TableCell>\n                          <TableCell\n                            className=\"text-right border-l\"\n                            rowSpan={details.length}\n                          >\n                            <div className=\"flex justify-end gap-2\">\n                              <Button\n                                variant=\"outline\"\n                                size=\"sm\"\n                                onClick={(e) => {\n                                  e.stopPropagation();\n                                  handleView(order);\n                                }}\n                              >\n                                <Eye className=\"h-4 w-4\" />\n                              </Button>\n                              <Button\n                                variant=\"outline\"\n                                size=\"sm\"\n                                onClick={(e) => {\n                                  e.stopPropagation();\n                                  handleEdit(order);\n                                }}\n                              >\n                                <Pencil className=\"h-4 w-4\" />\n                              </Button>\n                              <Button\n                                variant=\"outline\"\n                                size=\"sm\"\n                                onClick={(e) => {\n                                  e.stopPropagation();\n                                  setDeleteDialog({ open: true, order });\n                                }}\n                              >\n                                <Trash2 className=\"h-4 w-4\" />\n                              </Button>\n                            </div>\n                          </TableCell>\n                        </>\n                      ) : null}\n                    </TableRow>\n                  ));\n                })\n              )}\n            </TableBody>\n          </Table>\n        </div>\n\n        {/* 分页 */}\n        {pagination.pages > 1 && (\n          <div className=\"flex items-center justify-between mt-4\">\n            <div className=\"text-sm text-gray-500\">\n              共 {pagination.total} 条记录，第 {pagination.page} /{\" \"}\n              {pagination.pages} 页\n            </div>\n            <div className=\"flex gap-2\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                disabled={pagination.page <= 1}\n                onClick={() => handlePageChange(pagination.page - 1)}\n              >\n                上一页\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                disabled={pagination.page >= pagination.pages}\n                onClick={() => handlePageChange(pagination.page + 1)}\n              >\n                下一页\n              </Button>\n            </div>\n          </div>\n        )}\n      </CardContent>\n\n      {/* 删除确认对话框 */}\n      <Dialog\n        open={deleteDialog.open}\n        onOpenChange={(open) => setDeleteDialog({ open, order: null })}\n      >\n        <DialogContent>\n          <DialogHeader>\n            <DialogTitle>确认删除</DialogTitle>\n            <DialogDescription>\n              确定要删除订单 \"{deleteDialog.order?.id.slice(-8)}\"\n              吗？此操作将同时删除所有订单明细，且不可撤销。\n            </DialogDescription>\n          </DialogHeader>\n          <DialogFooter>\n            <Button\n              variant=\"outline\"\n              onClick={() => setDeleteDialog({ open: false, order: null })}\n            >\n              取消\n            </Button>\n            <Button variant=\"destructive\" onClick={handleDelete}>\n              删除\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* 订单表单对话框 */}\n      <OrderForm\n        open={orderForm.open}\n        onOpenChange={(open: boolean) => setOrderForm({ open, order: null })}\n        order={orderForm.order}\n        onSuccess={handleFormSuccess}\n      />\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAQA;AAQA;AAOA;AAOA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AAtCA;;;;;;;;;;;;AAwCA,MAAM,mBAAmB;IACvB,SAAS;QAAE,OAAO;QAAO,OAAO;IAAgC;IAChE,WAAW;QAAE,OAAO;QAAO,OAAO;IAA4B;IAC9D,SAAS;QAAE,OAAO;QAAO,OAAO;IAAgC;IAChE,WAAW;QAAE,OAAO;QAAO,OAAO;IAA8B;IAChE,WAAW;QAAE,OAAO;QAAO,OAAO;IAA0B;AAC9D;AAOe,SAAS,UAAU,EAChC,aAAa,EACb,UAAU,EACK;;IACf,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;QAC/D,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAG5C;QACD,MAAM;QACN,OAAO;IACT;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAGtC;QACD,MAAM;QACN,OAAO;IACT;IAEA,SAAS;IACT,MAAM,aAAa,OAAO,OAAO,CAAC,EAAE,SAAS,EAAE,EAAE,SAAS,EAAE;QAC1D,IAAI;YACF,WAAW;YACX,MAAM,SAAc;gBAClB;gBACA,OAAO,WAAW,KAAK;gBACvB,GAAI,UAAU;oBAAE;gBAAO,CAAC;gBACxB,GAAI,cAAc;oBAAE;gBAAW,CAAC;YAClC;YAEA,MAAM,WAAW,SACb,MAAM,oHAAA,CAAA,WAAQ,CAAC,YAAY,CAAC,QAAQ,UACpC,MAAM,oHAAA,CAAA,WAAQ,CAAC,SAAS,CAAC;YAE7B,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,UAAU,SAAS,IAAI,CAAC,MAAM;gBAC9B,cAAc,SAAS,IAAI,CAAC,UAAU;YACxC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,OAAO;IACP,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;QACF;8BAAG;QAAC;KAAW;IAEf,OAAO;IACP,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,WAAW,GAAG,aAAa;IAC7B;IAEA,SAAS;IACT,MAAM,qBAAqB,CAAC;QAC1B,MAAM,eAAe,WAAW,QAAQ,KAAK;QAC7C,gBAAgB;QAChB,WAAW,GAAG,aAAa;IAC7B;IAEA,OAAO;IACP,MAAM,YAAY;QAChB,aAAa;YAAE,MAAM;YAAM,OAAO;QAAK;IACzC;IAEA,OAAO;IACP,MAAM,aAAa,CAAC;QAClB,4BAA4B;QAC5B,IAAI,eAAe;YACjB,cAAc;QAChB,OAAO;YACL,cAAc;YACd,aAAa;gBAAE,MAAM;gBAAM;YAAM;QACnC;IACF;IAEA,OAAO;IACP,MAAM,aAAa,CAAC;QAClB,aAAa;YAAE,MAAM;YAAM;QAAM;IACnC;IAEA,SAAS;IACT,MAAM,oBAAoB;QACxB,WAAW,WAAW,IAAI,EAAE,aAAa;IAC3C;IAEA,OAAO;IACP,MAAM,eAAe;QACnB,IAAI,CAAC,aAAa,KAAK,EAAE;QAEzB,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,WAAW,CAAC,aAAa,KAAK,CAAC,EAAE;YACjE,IAAI,SAAS,OAAO,EAAE;gBACpB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,WAAW,WAAW,IAAI,EAAE,aAAa;gBACzC,gBAAgB;oBAAE,MAAM;oBAAO,OAAO;gBAAK;YAC7C;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WAAW;YACtD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,OAAO;IACP,MAAM,mBAAmB,CAAC;QACxB,WAAW,SAAS,aAAa;IACnC;IAEA,QAAQ;IACR,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC;IACjD;IAEA,QAAQ;IACR,MAAM,eAAe,CAAC;QACpB,MAAM,YAAY,OAAO,WAAW,WAAW,WAAW,UAAU;QACpE,OAAO,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,EAAE,OAAO,CAAC,IAAI;IAC1C;IAEA,QAAQ;IACR,MAAM,eAAe,CAAC;QACpB,MAAM,WAAW,OAAO,UAAU,WAAW,WAAW,SAAS;QACjE,OAAO,CAAC,YAAY,CAAC,EAAE,OAAO,CAAC;IACjC;IAEA,gBAAgB;IAChB,MAAM,cAAc,CAAC;QACnB,MAAM,YAAY,OAAO,WAAW,WAAW,WAAW,UAAU;QACpE,OAAO,CAAC,aAAa,CAAC,EAAE,OAAO,CAAC;IAClC;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;;0BACH,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGtC,6LAAC,mIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAEnB,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS;;8CACf,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;0BAKvC,6LAAC,mIAAA,CAAA,cAAW;;kCAEV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,UAAU;gCAAc,WAAU;;kDACtC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC,oIAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;;;;;;;kDAGd,6LAAC,qIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAQ;kDAAU;;;;;;;;;;;;0CAI1C,6LAAC,qIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAc,eAAe;;kDAC1C,6LAAC,qIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,6LAAC,qIAAA,CAAA,gBAAa;;0DACZ,6LAAC,qIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAM;;;;;;4CACvB,OAAO,OAAO,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC,QAAQ,OAAO,iBACrD,6LAAC,qIAAA,CAAA,aAAU;oDAAc,OAAO;8DAC7B,OAAO,KAAK;mDADE;;;;;;;;;;;;;;;;;;;;;;;kCASzB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;;8CACJ,6LAAC,oIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;0DACP,6LAAC,oIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,oIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,oIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,oIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,oIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,oIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAa;;;;;;0DAClC,6LAAC,oIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAa;;;;;;0DAClC,6LAAC,oIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAa;;;;;;0DAClC,6LAAC,oIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,oIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAa;;;;;;;;;;;;;;;;;8CAGtC,6LAAC,oIAAA,CAAA,YAAS;8CACP,wBACC,6LAAC,oIAAA,CAAA,WAAQ;kDACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;4CAAC,SAAS;4CAAI,WAAU;sDAAmB;;;;;;;;;;+CAIrD,OAAO,MAAM,KAAK,kBACpB,6LAAC,oIAAA,CAAA,WAAQ;kDACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;4CACR,SAAS;4CACT,WAAU;sDAET,eAAe,eACZ,aACA;;;;;;;;;;+CAIR,OAAO,GAAG,CAAC,CAAC;wCACV,MAAM,UAAU,MAAM,iBAAiB,IAAI,EAAE;wCAC7C,IAAI,QAAQ,MAAM,KAAK,GAAG;4CACxB,iBAAiB;4CACjB,qBACE,6LAAC,oIAAA,CAAA,WAAQ;gDAEP,WAAU;gDACV,SAAS,IAAM,gBAAgB;;kEAE/B,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,MAAM,EAAE,CAAC,KAAK,CAAC,CAAC;;;;;;kEAEnB,6LAAC,oIAAA,CAAA,YAAS;kEACP,MAAM,QAAQ,EAAE,gBAAgB;;;;;;kEAEnC,6LAAC,oIAAA,CAAA,YAAS;kEAAE,MAAM,gBAAgB,IAAI;;;;;;kEACtC,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;4DACC,WAAW,CAAC,2CAA2C,EACrD,gBAAgB,CAAC,MAAM,WAAW,CAAC,EAAE,SACrC,6BACA;sEAED,gBAAgB,CAAC,MAAM,WAAW,CAAC,EAAE,SACpC,MAAM,WAAW;;;;;;;;;;;kEAGvB,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAgB;;;;;;kEACrC,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAgB;;;;;;kEACrC,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAgB;;;;;;kEACrC,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAgB;;;;;;kEACrC,6LAAC,oIAAA,CAAA,YAAS;kEAAE,WAAW,MAAM,SAAS;;;;;;kEACtC,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;kEACnB,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,CAAC;wEACR,EAAE,eAAe;wEACjB,WAAW;oEACb;8EAEA,cAAA,6LAAC,mMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;;;;;;8EAEjB,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,CAAC;wEACR,EAAE,eAAe;wEACjB,WAAW;oEACb;8EAEA,cAAA,6LAAC,yMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;8EAEpB,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,CAAC;wEACR,EAAE,eAAe;wEACjB,gBAAgB;4EAAE,MAAM;4EAAM;wEAAM;oEACtC;8EAEA,cAAA,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+CAzDnB,MAAM,EAAE;;;;;wCA+DnB;wCAEA,kBAAkB;wCAClB,OAAO,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBAC1B,6LAAC,oIAAA,CAAA,WAAQ;gDAEP,WAAU;gDACV,SAAS,IAAM,gBAAgB;;oDAG9B,UAAU,kBACT;;0EACE,6LAAC,oIAAA,CAAA,YAAS;gEACR,WAAU;gEACV,SAAS,QAAQ,MAAM;0EAEtB,MAAM,EAAE,CAAC,KAAK,CAAC,CAAC;;;;;;0EAEnB,6LAAC,oIAAA,CAAA,YAAS;gEACR,WAAU;gEACV,SAAS,QAAQ,MAAM;0EAEtB,MAAM,QAAQ,EAAE,gBAAgB;;;;;;0EAEnC,6LAAC,oIAAA,CAAA,YAAS;gEACR,WAAU;gEACV,SAAS,QAAQ,MAAM;0EAEtB,MAAM,gBAAgB,IAAI;;;;;;0EAE7B,6LAAC,oIAAA,CAAA,YAAS;gEACR,WAAU;gEACV,SAAS,QAAQ,MAAM;0EAEvB,cAAA,6LAAC;oEACC,WAAW,CAAC,2CAA2C,EACrD,gBAAgB,CAAC,MAAM,WAAW,CAAC,EAAE,SACrC,6BACA;8EAED,gBAAgB,CAAC,MAAM,WAAW,CAAC,EAAE,SACpC,MAAM,WAAW;;;;;;;;;;;;uEAIvB;kEAGJ,6LAAC,oIAAA,CAAA,YAAS;kEACP,OAAO,QAAQ,EAAE,gBAAgB;;;;;;kEAEpC,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;;4DAClB,aAAa,OAAO,QAAQ;4DAAE;4DAAE,OAAO,IAAI;;;;;;;kEAE9C,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,YAAY,OAAO,SAAS;;;;;;kEAE/B,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,YAAY,OAAO,WAAW;;;;;;oDAIhC,UAAU,kBACT;;0EACE,6LAAC,oIAAA,CAAA,YAAS;gEACR,WAAU;gEACV,SAAS,QAAQ,MAAM;0EAEtB,WAAW,MAAM,SAAS;;;;;;0EAE7B,6LAAC,oIAAA,CAAA,YAAS;gEACR,WAAU;gEACV,SAAS,QAAQ,MAAM;0EAEvB,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,qIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,MAAK;4EACL,SAAS,CAAC;gFACR,EAAE,eAAe;gFACjB,WAAW;4EACb;sFAEA,cAAA,6LAAC,mMAAA,CAAA,MAAG;gFAAC,WAAU;;;;;;;;;;;sFAEjB,6LAAC,qIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,MAAK;4EACL,SAAS,CAAC;gFACR,EAAE,eAAe;gFACjB,WAAW;4EACb;sFAEA,cAAA,6LAAC,yMAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;;;;;;sFAEpB,6LAAC,qIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,MAAK;4EACL,SAAS,CAAC;gFACR,EAAE,eAAe;gFACjB,gBAAgB;oFAAE,MAAM;oFAAM;gFAAM;4EACtC;sFAEA,cAAA,6LAAC,6MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;uEAKxB;;+CAvGC,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE;;;;;oCA0GrC;;;;;;;;;;;;;;;;;oBAOP,WAAW,KAAK,GAAG,mBAClB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCAAwB;oCAClC,WAAW,KAAK;oCAAC;oCAAQ,WAAW,IAAI;oCAAC;oCAAG;oCAC9C,WAAW,KAAK;oCAAC;;;;;;;0CAEpB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,UAAU,WAAW,IAAI,IAAI;wCAC7B,SAAS,IAAM,iBAAiB,WAAW,IAAI,GAAG;kDACnD;;;;;;kDAGD,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,UAAU,WAAW,IAAI,IAAI,WAAW,KAAK;wCAC7C,SAAS,IAAM,iBAAiB,WAAW,IAAI,GAAG;kDACnD;;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC,qIAAA,CAAA,SAAM;gBACL,MAAM,aAAa,IAAI;gBACvB,cAAc,CAAC,OAAS,gBAAgB;wBAAE;wBAAM,OAAO;oBAAK;0BAE5D,cAAA,6LAAC,qIAAA,CAAA,gBAAa;;sCACZ,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,6LAAC,qIAAA,CAAA,oBAAiB;;wCAAC;wCACP,aAAa,KAAK,EAAE,GAAG,MAAM,CAAC;wCAAG;;;;;;;;;;;;;sCAI/C,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,gBAAgB;4CAAE,MAAM;4CAAO,OAAO;wCAAK;8CAC3D;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAc,SAAS;8CAAc;;;;;;;;;;;;;;;;;;;;;;;0BAQ3D,6LAAC,4IAAA,CAAA,UAAS;gBACR,MAAM,UAAU,IAAI;gBACpB,cAAc,CAAC,OAAkB,aAAa;wBAAE;wBAAM,OAAO;oBAAK;gBAClE,OAAO,UAAU,KAAK;gBACtB,WAAW;;;;;;;;;;;;AAInB;GAlewB;KAAA", "debugId": null}}, {"offset": {"line": 4874, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/MyProject/MyApp-OrderList/frontend/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from \"react\";\nimport { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from \"@/components/ui/tabs\";\nimport CustomerList from \"@/components/customers/CustomerList\";\nimport MaterialList from \"@/components/materials/MaterialList\";\nimport OrderList from \"@/components/orders/OrderList\";\nimport { SalesOrder } from \"@/lib/api\";\n\nexport default function Home() {\n  const [selectedOrder, setSelectedOrder] = useState<SalesOrder | null>(null);\n\n  const handleOrderSelect = (order: SalesOrder) => {\n    setSelectedOrder(order);\n    // 这里可以打开订单详情对话框或导航到详情页面\n    console.log(\"Selected order:\", order);\n  };\n\n  return (\n    <div className=\"container mx-auto p-6\">\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900\">订单管理系统</h1>\n        <p className=\"text-gray-600 mt-2\">管理客户、物料和销售订单</p>\n      </div>\n\n      <Tabs defaultValue=\"orders\" className=\"w-full\">\n        <TabsList className=\"grid w-full grid-cols-3\">\n          <TabsTrigger value=\"orders\">订单管理</TabsTrigger>\n          <TabsTrigger value=\"customers\">客户管理</TabsTrigger>\n          <TabsTrigger value=\"materials\">物料管理</TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"orders\" className=\"mt-6\">\n          <OrderList onOrderSelect={handleOrderSelect} />\n        </TabsContent>\n\n        <TabsContent value=\"customers\" className=\"mt-6\">\n          <CustomerList />\n        </TabsContent>\n\n        <TabsContent value=\"materials\" className=\"mt-6\">\n          <MaterialList />\n        </TabsContent>\n      </Tabs>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AASe,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAEtE,MAAM,oBAAoB,CAAC;QACzB,iBAAiB;QACjB,wBAAwB;QACxB,QAAQ,GAAG,CAAC,mBAAmB;IACjC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;0BAGpC,6LAAC,mIAAA,CAAA,OAAI;gBAAC,cAAa;gBAAS,WAAU;;kCACpC,6LAAC,mIAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAS;;;;;;0CAC5B,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAY;;;;;;0CAC/B,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAY;;;;;;;;;;;;kCAGjC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAS,WAAU;kCACpC,cAAA,6LAAC,4IAAA,CAAA,UAAS;4BAAC,eAAe;;;;;;;;;;;kCAG5B,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAY,WAAU;kCACvC,cAAA,6LAAC,kJAAA,CAAA,UAAY;;;;;;;;;;kCAGf,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAY,WAAU;kCACvC,cAAA,6LAAC,kJAAA,CAAA,UAAY;;;;;;;;;;;;;;;;;;;;;;AAKvB;GArCwB;KAAA", "debugId": null}}]}