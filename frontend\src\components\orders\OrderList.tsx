"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { orderApi, SalesOrder, PaginationResponse } from "@/lib/api";
import { Pencil, Trash2, Plus, Search, ShoppingCart, Eye } from "lucide-react";
import { toast } from "sonner";
import OrderForm from "./OrderForm";

const ORDER_STATUS_MAP = {
  PENDING: { label: "待确认", color: "bg-yellow-100 text-yellow-800" },
  CONFIRMED: { label: "已确认", color: "bg-blue-100 text-blue-800" },
  SHIPPED: { label: "已发货", color: "bg-purple-100 text-purple-800" },
  DELIVERED: { label: "已交付", color: "bg-green-100 text-green-800" },
  CANCELLED: { label: "已取消", color: "bg-red-100 text-red-800" },
};

interface OrderListProps {
  onOrderSelect?: (order: SalesOrder) => void;
  customerId?: string;
}

export default function OrderList({
  onOrderSelect,
  customerId,
}: OrderListProps) {
  const [orders, setOrders] = useState<SalesOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("ALL");
  const [pagination, setPagination] = useState<PaginationResponse>({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0,
  });
  const [deleteDialog, setDeleteDialog] = useState<{
    open: boolean;
    order: SalesOrder | null;
  }>({
    open: false,
    order: null,
  });
  const [orderForm, setOrderForm] = useState<{
    open: boolean;
    order: SalesOrder | null;
    readOnly: boolean;
  }>({
    open: false,
    order: null,
    readOnly: false,
  });

  // 加载订单列表
  const loadOrders = async (page = 1, search = "", status = "") => {
    try {
      setLoading(true);
      const params: any = {
        page,
        limit: pagination.limit,
        ...(status && { status }),
        ...(customerId && { customerId }),
      };

      const response = search
        ? await orderApi.searchOrders(search, params)
        : await orderApi.getOrders(params);

      if (response.success && response.data) {
        setOrders(response.data.orders);
        setPagination(response.data.pagination);
      }
    } catch (error) {
      console.error("Failed to load orders:", error);
      toast.error("加载订单列表失败");
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    loadOrders();
  }, [customerId]);

  // 搜索处理
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    loadOrders(1, searchQuery, statusFilter);
  };

  // 状态筛选处理
  const handleStatusFilter = (status: string) => {
    const filterStatus = status === "ALL" ? "" : status;
    setStatusFilter(status);
    loadOrders(1, searchQuery, filterStatus);
  };

  // 新增订单
  const handleAdd = () => {
    setOrderForm({ open: true, order: null, readOnly: false });
  };

  // 查看订单
  const handleView = (order: SalesOrder) => {
    console.log("handleView called with order:", order.id);
    // 如果有外部的onOrderSelect回调，使用它
    if (onOrderSelect) {
      console.log("Using external onOrderSelect callback");
      onOrderSelect(order);
    } else {
      // 否则以只读模式打开表单
      console.log("Opening form in read-only mode");
      setOrderForm({ open: true, order, readOnly: true });
    }
  };

  // 编辑订单
  const handleEdit = (order: SalesOrder) => {
    setOrderForm({ open: true, order, readOnly: false });
  };

  // 表单成功回调
  const handleFormSuccess = () => {
    loadOrders(pagination.page, searchQuery, statusFilter);
  };

  // 删除订单
  const handleDelete = async () => {
    if (!deleteDialog.order) return;

    try {
      const response = await orderApi.deleteOrder(deleteDialog.order.id);
      if (response.success) {
        toast.success("订单删除成功");
        loadOrders(pagination.page, searchQuery, statusFilter);
        setDeleteDialog({ open: false, order: null });
      }
    } catch (error: any) {
      console.error("Failed to delete order:", error);
      const errorMessage = error.response?.data?.message || "删除订单失败";
      toast.error(errorMessage);
    }
  };

  // 分页处理
  const handlePageChange = (newPage: number) => {
    loadOrders(newPage, searchQuery, statusFilter);
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("zh-CN");
  };

  // 格式化金额
  const formatAmount = (amount: number | string) => {
    const numAmount = typeof amount === "string" ? parseFloat(amount) : amount;
    return `¥${(numAmount || 0).toFixed(2)}`;
  };

  // 格式化数量
  const formatNumber = (value: number | string) => {
    const numValue = typeof value === "string" ? parseFloat(value) : value;
    return (numValue || 0).toFixed(2);
  };

  // 格式化价格（不带货币符号）
  const formatPrice = (amount: number | string) => {
    const numAmount = typeof amount === "string" ? parseFloat(amount) : amount;
    return (numAmount || 0).toFixed(2);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <ShoppingCart className="h-5 w-5" />
              订单管理
            </CardTitle>
            <CardDescription>管理销售订单和订单明细</CardDescription>
          </div>
          <Button onClick={handleAdd}>
            <Plus className="h-4 w-4 mr-2" />
            新增订单
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {/* 搜索和筛选栏 */}
        <div className="flex gap-2 mb-4">
          <form onSubmit={handleSearch} className="flex gap-2 flex-1">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="搜索订单号或客户名称..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button type="submit" variant="outline">
              搜索
            </Button>
          </form>
          <Select value={statusFilter} onValueChange={handleStatusFilter}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="状态筛选" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">全部状态</SelectItem>
              {Object.entries(ORDER_STATUS_MAP).map(([status, config]) => (
                <SelectItem key={status} value={status}>
                  {config.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* 订单表格 */}
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>订单ID</TableHead>
                <TableHead>客户名称</TableHead>
                <TableHead>客户订单号</TableHead>
                <TableHead>订单状态</TableHead>
                <TableHead>物料名称</TableHead>
                <TableHead className="text-right">数量/单位</TableHead>
                <TableHead className="text-right">单价(¥)</TableHead>
                <TableHead className="text-right">明细金额(¥)</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={10} className="text-center py-8">
                    加载中...
                  </TableCell>
                </TableRow>
              ) : orders.length === 0 ? (
                <TableRow>
                  <TableCell
                    colSpan={10}
                    className="text-center py-8 text-gray-500"
                  >
                    {searchQuery || statusFilter
                      ? "未找到匹配的订单"
                      : "暂无订单数据"}
                  </TableCell>
                </TableRow>
              ) : (
                orders.map((order) => {
                  const details = order.salesOrderDetails || [];
                  if (details.length === 0) {
                    // 如果没有明细，显示一行空明细
                    return (
                      <TableRow
                        key={order.id}
                        className="cursor-pointer hover:bg-gray-50"
                        onClick={() => onOrderSelect?.(order)}
                      >
                        <TableCell className="font-medium font-mono text-sm">
                          {order.id.slice(-8)}
                        </TableCell>
                        <TableCell>
                          {order.customer?.customerName || "未知客户"}
                        </TableCell>
                        <TableCell>{order.customerPoNumber || "-"}</TableCell>
                        <TableCell>
                          <span
                            className={`px-2 py-1 rounded-full text-xs font-medium ${
                              ORDER_STATUS_MAP[order.orderStatus]?.color ||
                              "bg-gray-100 text-gray-800"
                            }`}
                          >
                            {ORDER_STATUS_MAP[order.orderStatus]?.label ||
                              order.orderStatus}
                          </span>
                        </TableCell>
                        <TableCell className="text-gray-500">-</TableCell>
                        <TableCell className="text-gray-500">-</TableCell>
                        <TableCell className="text-gray-500">-</TableCell>
                        <TableCell className="text-gray-500">-</TableCell>
                        <TableCell>{formatDate(order.createdAt)}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleView(order);
                              }}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEdit(order);
                              }}
                            >
                              <Pencil className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                setDeleteDialog({ open: true, order });
                              }}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  }

                  // 如果有明细，为每个明细显示一行
                  return details.map((detail, index) => (
                    <TableRow
                      key={`${order.id}-${detail.id}`}
                      className="cursor-pointer hover:bg-gray-50"
                      onClick={() => onOrderSelect?.(order)}
                    >
                      {/* 只在第一行显示订单基本信息 */}
                      {index === 0 ? (
                        <>
                          <TableCell
                            className="font-medium font-mono text-sm border-r"
                            rowSpan={details.length}
                          >
                            {order.id.slice(-8)}
                          </TableCell>
                          <TableCell
                            className="border-r"
                            rowSpan={details.length}
                          >
                            {order.customer?.customerName || "未知客户"}
                          </TableCell>
                          <TableCell
                            className="border-r"
                            rowSpan={details.length}
                          >
                            {order.customerPoNumber || "-"}
                          </TableCell>
                          <TableCell
                            className="border-r"
                            rowSpan={details.length}
                          >
                            <span
                              className={`px-2 py-1 rounded-full text-xs font-medium ${
                                ORDER_STATUS_MAP[order.orderStatus]?.color ||
                                "bg-gray-100 text-gray-800"
                              }`}
                            >
                              {ORDER_STATUS_MAP[order.orderStatus]?.label ||
                                order.orderStatus}
                            </span>
                          </TableCell>
                        </>
                      ) : null}

                      {/* 明细信息 */}
                      <TableCell>
                        {detail.material?.materialName || "未知物料"}
                      </TableCell>
                      <TableCell className="text-right">
                        {formatNumber(detail.quantity)} {detail.unit}
                      </TableCell>
                      <TableCell className="text-right">
                        {formatPrice(detail.unitPrice)}
                      </TableCell>
                      <TableCell className="text-right font-medium">
                        {formatPrice(detail.totalAmount)}
                      </TableCell>

                      {/* 只在第一行显示创建时间和操作按钮 */}
                      {index === 0 ? (
                        <>
                          <TableCell
                            className="border-l"
                            rowSpan={details.length}
                          >
                            {formatDate(order.createdAt)}
                          </TableCell>
                          <TableCell
                            className="text-right border-l"
                            rowSpan={details.length}
                          >
                            <div className="flex justify-end gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleView(order);
                                }}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleEdit(order);
                                }}
                              >
                                <Pencil className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setDeleteDialog({ open: true, order });
                                }}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </>
                      ) : null}
                    </TableRow>
                  ));
                })
              )}
            </TableBody>
          </Table>
        </div>

        {/* 分页 */}
        {pagination.pages > 1 && (
          <div className="flex items-center justify-between mt-4">
            <div className="text-sm text-gray-500">
              共 {pagination.total} 条记录，第 {pagination.page} /{" "}
              {pagination.pages} 页
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                disabled={pagination.page <= 1}
                onClick={() => handlePageChange(pagination.page - 1)}
              >
                上一页
              </Button>
              <Button
                variant="outline"
                size="sm"
                disabled={pagination.page >= pagination.pages}
                onClick={() => handlePageChange(pagination.page + 1)}
              >
                下一页
              </Button>
            </div>
          </div>
        )}
      </CardContent>

      {/* 删除确认对话框 */}
      <Dialog
        open={deleteDialog.open}
        onOpenChange={(open) => setDeleteDialog({ open, order: null })}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              确定要删除订单 "{deleteDialog.order?.id.slice(-8)}"
              吗？此操作将同时删除所有订单明细，且不可撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialog({ open: false, order: null })}
            >
              取消
            </Button>
            <Button variant="destructive" onClick={handleDelete}>
              删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 订单表单对话框 */}
      <OrderForm
        open={orderForm.open}
        onOpenChange={(open: boolean) =>
          setOrderForm({ open, order: null, readOnly: false })
        }
        order={orderForm.order}
        onSuccess={handleFormSuccess}
        readOnly={orderForm.readOnly}
      />
    </Card>
  );
}
