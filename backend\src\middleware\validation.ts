import { Request, Response, NextFunction } from 'express';
import { AppError } from './errorHandler';

// 验证必填字段
export const validateRequired = (fields: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const missingFields: string[] = [];

    fields.forEach(field => {
      if (!req.body[field] || req.body[field] === '') {
        missingFields.push(field);
      }
    });

    if (missingFields.length > 0) {
      throw new AppError(`Missing required fields: ${missingFields.join(', ')}`, 400);
    }

    next();
  };
};

// 验证客户数据
export const validateCustomer = (req: Request, res: Response, next: NextFunction) => {
  const { customerName, contactPerson, address } = req.body;

  if (customerName && typeof customerName !== 'string') {
    throw new AppError('Customer name must be a string', 400);
  }

  if (contactPerson && typeof contactPerson !== 'string') {
    throw new AppError('Contact person must be a string', 400);
  }

  if (address && typeof address !== 'string') {
    throw new AppError('Address must be a string', 400);
  }

  next();
};

// 验证物料数据
export const validateMaterial = (req: Request, res: Response, next: NextFunction) => {
  const { materialName, baseUnit } = req.body;

  if (materialName && typeof materialName !== 'string') {
    throw new AppError('Material name must be a string', 400);
  }

  if (baseUnit && typeof baseUnit !== 'string') {
    throw new AppError('Base unit must be a string', 400);
  }

  next();
};

// 验证价格数据
export const validateMaterialPrice = (req: Request, res: Response, next: NextFunction) => {
  const { materialId, unitPrice, currency } = req.body;

  if (materialId && typeof materialId !== 'string') {
    throw new AppError('Material ID must be a string', 400);
  }

  if (unitPrice && (typeof unitPrice !== 'number' || unitPrice <= 0)) {
    throw new AppError('Unit price must be a positive number', 400);
  }

  if (currency && typeof currency !== 'string') {
    throw new AppError('Currency must be a string', 400);
  }

  next();
};

// 验证订单数据
export const validateSalesOrder = (req: Request, res: Response, next: NextFunction) => {
  const { customerId, orderDetails } = req.body;

  if (customerId && typeof customerId !== 'string') {
    throw new AppError('Customer ID must be a string', 400);
  }

  if (orderDetails && !Array.isArray(orderDetails)) {
    throw new AppError('Order details must be an array', 400);
  }

  if (orderDetails && orderDetails.length === 0) {
    throw new AppError('Order must have at least one detail item', 400);
  }

  // 验证订单明细
  if (orderDetails) {
    orderDetails.forEach((detail: any, index: number) => {
      if (!detail.materialId || typeof detail.materialId !== 'string') {
        throw new AppError(`Order detail ${index + 1}: Material ID is required and must be a string`, 400);
      }

      if (!detail.quantity || typeof detail.quantity !== 'number' || detail.quantity <= 0) {
        throw new AppError(`Order detail ${index + 1}: Quantity must be a positive number`, 400);
      }

      if (!detail.unit || typeof detail.unit !== 'string') {
        throw new AppError(`Order detail ${index + 1}: Unit is required and must be a string`, 400);
      }

      if (!detail.unitPrice || typeof detail.unitPrice !== 'number' || detail.unitPrice <= 0) {
        throw new AppError(`Order detail ${index + 1}: Unit price must be a positive number`, 400);
      }
    });
  }

  next();
};

// 验证分页参数
export const validatePagination = (req: Request, res: Response, next: NextFunction) => {
  const { page, limit } = req.query;

  if (page && (isNaN(Number(page)) || Number(page) < 1)) {
    throw new AppError('Page must be a positive integer', 400);
  }

  if (limit && (isNaN(Number(limit)) || Number(limit) < 1 || Number(limit) > 100)) {
    throw new AppError('Limit must be a positive integer between 1 and 100', 400);
  }

  next();
};
