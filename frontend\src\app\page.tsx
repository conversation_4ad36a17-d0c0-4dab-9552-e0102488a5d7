"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import CustomerList from "@/components/customers/CustomerList";
import MaterialList from "@/components/materials/MaterialList";
import PriceList from "@/components/prices/PriceList";
import OrderList from "@/components/orders/OrderList";
import { SalesOrder } from "@/lib/api";

export default function Home() {
  const [selectedOrder, setSelectedOrder] = useState<SalesOrder | null>(null);

  const handleOrderSelect = (order: SalesOrder) => {
    setSelectedOrder(order);
    // 这里可以打开订单详情对话框或导航到详情页面
    console.log("Selected order:", order);
  };

  return (
    <div className="container mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">订单管理系统</h1>
        <p className="text-gray-600 mt-2">管理客户、物料、价格和销售订单</p>
      </div>

      <Tabs defaultValue="orders" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="orders">订单管理</TabsTrigger>
          <TabsTrigger value="customers">客户管理</TabsTrigger>
          <TabsTrigger value="materials">物料管理</TabsTrigger>
          <TabsTrigger value="prices">价格管理</TabsTrigger>
        </TabsList>

        <TabsContent value="orders" className="mt-6">
          <OrderList onOrderSelect={handleOrderSelect} />
        </TabsContent>

        <TabsContent value="customers" className="mt-6">
          <CustomerList />
        </TabsContent>

        <TabsContent value="materials" className="mt-6">
          <MaterialList />
        </TabsContent>

        <TabsContent value="prices" className="mt-6">
          <PriceList />
        </TabsContent>
      </Tabs>
    </div>
  );
}
