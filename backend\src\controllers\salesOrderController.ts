import { Request, Response } from "express";
import prisma from "../lib/prisma";
import {
  ApiResponse,
  CreateSalesOrderRequest,
  UpdateSalesOrderRequest,
} from "../types";
import { AppError, asyncHandler } from "../middleware/errorHandler";
import { Decimal } from "@prisma/client/runtime/library";

// 获取所有订单（分页）
export const getSalesOrders = asyncHandler(
  async (req: Request, res: Response) => {
    const {
      page = 1,
      limit = 10,
      sortBy = "createdAt",
      sortOrder = "desc",
      status,
      customerId,
    } = req.query as any;

    const skip = (Number(page) - 1) * Number(limit);

    // 构建查询条件
    const whereCondition: any = {};
    if (status) whereCondition.orderStatus = status;
    if (customerId) whereCondition.customerId = customerId;

    const [orders, total] = await Promise.all([
      prisma.salesOrder.findMany({
        where: whereCondition,
        skip,
        take: Number(limit),
        orderBy: {
          [sortBy]: sortOrder,
        },
        include: {
          customer: {
            select: {
              customerName: true,
              contactPerson: true,
            },
          },
          _count: {
            select: {
              salesOrderDetails: true,
            },
          },
        },
      }),
      prisma.salesOrder.count({
        where: whereCondition,
      }),
    ]);

    const response: ApiResponse = {
      success: true,
      message: "Sales orders retrieved successfully",
      data: {
        orders,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit)),
        },
      },
    };

    res.json(response);
  }
);

// 根据ID获取订单详情
export const getSalesOrderById = asyncHandler(
  async (req: Request, res: Response) => {
    const { id } = req.params;

    const order = await prisma.salesOrder.findUnique({
      where: { id },
      include: {
        customer: true,
        salesOrderDetails: {
          include: {
            material: {
              select: {
                materialName: true,
                baseUnit: true,
              },
            },
          },
          orderBy: {
            createdAt: "asc",
          },
        },
      },
    });

    if (!order) {
      throw new AppError("Sales order not found", 404);
    }

    const response: ApiResponse = {
      success: true,
      message: "Sales order retrieved successfully",
      data: order,
    };

    res.json(response);
  }
);

// 创建新订单
export const createSalesOrder = asyncHandler(
  async (req: Request, res: Response) => {
    const {
      customerId,
      customerPoNumber,
      orderDetails,
    }: CreateSalesOrderRequest = req.body;

    // 验证客户是否存在
    const customer = await prisma.customer.findUnique({
      where: { id: customerId },
    });

    if (!customer) {
      throw new AppError("Customer not found", 404);
    }

    // 验证所有物料是否存在
    const materialIds = orderDetails.map((detail) => detail.materialId);
    const materials = await prisma.material.findMany({
      where: {
        id: {
          in: materialIds,
        },
      },
    });

    if (materials.length !== materialIds.length) {
      throw new AppError("One or more materials not found", 404);
    }

    // 计算订单总金额
    const totalAmount = orderDetails.reduce((sum, detail) => {
      return sum + detail.quantity * detail.unitPrice;
    }, 0);

    // 使用事务创建订单和明细
    const order = await prisma.$transaction(async (tx) => {
      // 创建订单主记录
      const newOrder = await tx.salesOrder.create({
        data: {
          customerId,
          customerPoNumber,
          totalAmount: new Decimal(totalAmount),
          orderStatus: "PENDING",
        },
      });

      // 创建订单明细
      const orderDetailsData = orderDetails.map((detail) => ({
        orderId: newOrder.id,
        materialId: detail.materialId,
        quantity: new Decimal(detail.quantity),
        unit: detail.unit,
        unitPrice: new Decimal(detail.unitPrice),
        totalAmount: new Decimal(detail.quantity * detail.unitPrice),
      }));

      await tx.salesOrderDetail.createMany({
        data: orderDetailsData,
      });

      // 返回完整的订单信息
      return await tx.salesOrder.findUnique({
        where: { id: newOrder.id },
        include: {
          customer: true,
          salesOrderDetails: {
            include: {
              material: {
                select: {
                  materialName: true,
                  baseUnit: true,
                },
              },
            },
          },
        },
      });
    });

    const response: ApiResponse = {
      success: true,
      message: "Sales order created successfully",
      data: order,
    };

    res.status(201).json(response);
  }
);

// 更新订单
export const updateSalesOrder = asyncHandler(
  async (req: Request, res: Response) => {
    const { id } = req.params;
    const updateData: UpdateSalesOrderRequest = req.body;

    // 检查订单是否存在
    const existingOrder = await prisma.salesOrder.findUnique({
      where: { id },
    });

    if (!existingOrder) {
      throw new AppError("Sales order not found", 404);
    }

    // 如果更新了客户ID，验证客户是否存在
    if (updateData.customerId) {
      const customer = await prisma.customer.findUnique({
        where: { id: updateData.customerId },
      });

      if (!customer) {
        throw new AppError("Customer not found", 404);
      }
    }

    const order = await prisma.salesOrder.update({
      where: { id },
      data: updateData,
      include: {
        customer: true,
        salesOrderDetails: {
          include: {
            material: {
              select: {
                materialName: true,
                baseUnit: true,
              },
            },
          },
        },
      },
    });

    const response: ApiResponse = {
      success: true,
      message: "Sales order updated successfully",
      data: order,
    };

    res.json(response);
  }
);

// 删除订单
export const deleteSalesOrder = asyncHandler(
  async (req: Request, res: Response) => {
    const { id } = req.params;

    // 检查订单是否存在
    const existingOrder = await prisma.salesOrder.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            salesOrderDetails: true,
          },
        },
      },
    });

    if (!existingOrder) {
      throw new AppError("Sales order not found", 404);
    }

    // 使用事务删除订单和明细
    await prisma.$transaction(async (tx) => {
      // 先删除订单明细
      await tx.salesOrderDetail.deleteMany({
        where: { orderId: id },
      });

      // 再删除订单主记录
      await tx.salesOrder.delete({
        where: { id },
      });
    });

    const response: ApiResponse = {
      success: true,
      message: "Sales order deleted successfully",
    };

    res.json(response);
  }
);

// 搜索订单
export const searchSalesOrders = asyncHandler(
  async (req: Request, res: Response) => {
    const { q, page = 1, limit = 10, status } = req.query as any;

    if (!q) {
      throw new AppError("Search query is required", 400);
    }

    const skip = (Number(page) - 1) * Number(limit);

    // 构建查询条件
    const whereCondition: any = {
      OR: [
        {
          customerPoNumber: {
            contains: q,
            mode: "insensitive",
          },
        },
        {
          customer: {
            customerName: {
              contains: q,
              mode: "insensitive",
            },
          },
        },
      ],
    };

    if (status) {
      whereCondition.orderStatus = status;
    }

    const [orders, total] = await Promise.all([
      prisma.salesOrder.findMany({
        where: whereCondition,
        skip,
        take: Number(limit),
        orderBy: {
          createdAt: "desc",
        },
        include: {
          customer: {
            select: {
              customerName: true,
              contactPerson: true,
            },
          },
          _count: {
            select: {
              salesOrderDetails: true,
            },
          },
        },
      }),
      prisma.salesOrder.count({
        where: whereCondition,
      }),
    ]);

    const response: ApiResponse = {
      success: true,
      message: "Sales orders search completed",
      data: {
        orders,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit)),
        },
      },
    };

    res.json(response);
  }
);

// 获取订单统计信息
export const getOrderStatistics = asyncHandler(
  async (req: Request, res: Response) => {
    const { startDate, endDate } = req.query as any;

    // 构建日期查询条件
    const dateCondition: any = {};
    if (startDate) dateCondition.gte = new Date(startDate);
    if (endDate) dateCondition.lte = new Date(endDate);

    const whereCondition =
      Object.keys(dateCondition).length > 0 ? { createdAt: dateCondition } : {};

    const [
      totalOrders,
      ordersByStatus,
      totalAmount,
      topCustomers,
      topMaterials,
    ] = await Promise.all([
      // 总订单数
      prisma.salesOrder.count({
        where: whereCondition,
      }),

      // 按状态分组的订单数
      prisma.salesOrder.groupBy({
        by: ["orderStatus"],
        where: whereCondition,
        _count: {
          id: true,
        },
        _sum: {
          totalAmount: true,
        },
      }),

      // 总金额
      prisma.salesOrder.aggregate({
        where: whereCondition,
        _sum: {
          totalAmount: true,
        },
      }),

      // 前5名客户（按订单数）
      prisma.customer.findMany({
        include: {
          _count: {
            select: {
              salesOrders: true,
            },
          },
          salesOrders: {
            where: whereCondition,
            select: {
              totalAmount: true,
            },
          },
        },
        orderBy: {
          salesOrders: {
            _count: "desc",
          },
        },
        take: 5,
      }),

      // 前5名物料（按订单明细数）
      prisma.material.findMany({
        include: {
          _count: {
            select: {
              salesOrderDetails: true,
            },
          },
          salesOrderDetails: {
            where: {
              salesOrder: whereCondition,
            },
            select: {
              quantity: true,
              totalAmount: true,
            },
          },
        },
        orderBy: {
          salesOrderDetails: {
            _count: "desc",
          },
        },
        take: 5,
      }),
    ]);

    const response: ApiResponse = {
      success: true,
      message: "Order statistics retrieved successfully",
      data: {
        summary: {
          totalOrders,
          totalAmount: totalAmount._sum.totalAmount || 0,
          ordersByStatus,
        },
        topCustomers: topCustomers.map((customer) => ({
          ...customer,
          totalOrderAmount: customer.salesOrders.reduce(
            (sum, order) => sum + Number(order.totalAmount),
            0
          ),
        })),
        topMaterials: topMaterials.map((material) => ({
          ...material,
          totalQuantity: material.salesOrderDetails.reduce(
            (sum, detail) => sum + Number(detail.quantity),
            0
          ),
          totalAmount: material.salesOrderDetails.reduce(
            (sum, detail) => sum + Number(detail.totalAmount),
            0
          ),
        })),
      },
    };

    res.json(response);
  }
);
