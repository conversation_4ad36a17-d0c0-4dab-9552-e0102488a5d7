// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 客户资料表
model Customer {
  id            String   @id @default(cuid())
  customerName  String   @map("customer_name") @db.VarChar(100)
  contactPerson String   @map("contact_person") @db.VarChar(50)
  address       String   @db.Text
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // 关系：一个客户可以有多个订单
  salesOrders SalesOrder[]

  @@map("customers")
}

// 商品物料表
model Material {
  id           String   @id @default(cuid())
  materialName String   @map("material_name") @db.VarChar(100)
  baseUnit     String   @map("base_unit") @db.VarChar(20)
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // 关系：一个物料可以有多个价格记录和订单明细
  materialPrices     MaterialPrice[]
  salesOrderDetails  SalesOrderDetail[]

  @@map("materials")
}

// 商品价格表
model MaterialPrice {
  id            String   @id @default(cuid())
  materialId    String   @map("material_id")
  unitPrice     Decimal  @map("unit_price") @db.Decimal(10, 2)
  currency      String   @db.VarChar(10)
  effectiveDate DateTime @default(now()) @map("effective_date")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // 关系：价格属于某个物料
  material Material @relation(fields: [materialId], references: [id], onDelete: Cascade)

  @@map("material_prices")
}

// 销售订单表
model SalesOrder {
  id                 String      @id @default(cuid())
  customerId         String      @map("customer_id")
  customerPoNumber   String?     @map("customer_po_number") @db.VarChar(50)
  orderStatus        OrderStatus @default(PENDING) @map("order_status")
  totalAmount        Decimal     @default(0) @map("total_amount") @db.Decimal(12, 2)
  createdAt          DateTime    @default(now()) @map("created_at")
  updatedAt          DateTime    @updatedAt @map("updated_at")

  // 关系：订单属于某个客户，一个订单可以有多个明细
  customer           Customer             @relation(fields: [customerId], references: [id], onDelete: Restrict)
  salesOrderDetails  SalesOrderDetail[]

  @@map("sales_orders")
}

// 销售订单明细表
model SalesOrderDetail {
  id          String  @id @default(cuid())
  orderId     String  @map("order_id")
  materialId  String  @map("material_id")
  quantity    Decimal @db.Decimal(10, 3)
  unit        String  @db.VarChar(20)
  unitPrice   Decimal @map("unit_price") @db.Decimal(10, 2)
  totalAmount Decimal @map("total_amount") @db.Decimal(12, 2)
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关系：明细属于某个订单和某个物料
  salesOrder SalesOrder @relation(fields: [orderId], references: [id], onDelete: Cascade)
  material   Material   @relation(fields: [materialId], references: [id], onDelete: Restrict)

  @@map("sales_order_details")
}

// 订单状态枚举
enum OrderStatus {
  PENDING     // 待确认
  CONFIRMED   // 已确认
  PRODUCING   // 生产中
  SHIPPED     // 已发货
  COMPLETED   // 已完成
  CANCELLED   // 已取消
}
