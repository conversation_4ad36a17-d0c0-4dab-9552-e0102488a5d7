import axios from "axios";

// API基础配置
const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// 响应拦截器
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error("API Error:", error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// 类型定义
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export interface PaginationResponse {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

// 客户相关类型
export interface Customer {
  id: string;
  customerName: string;
  contactPerson: string;
  address: string;
  createdAt: string;
  updatedAt: string;
  _count?: {
    salesOrders: number;
  };
}

export interface CreateCustomerRequest {
  customerName: string;
  contactPerson: string;
  address: string;
}

// 物料相关类型
export interface Material {
  id: string;
  materialName: string;
  baseUnit: string;
  createdAt: string;
  updatedAt: string;
  materialPrices?: MaterialPrice[];
}

export interface MaterialPrice {
  id: string;
  materialId: string;
  unitPrice: number;
  currency: string;
  effectiveDate: string;
  createdAt: string;
  updatedAt: string;
  material?: {
    materialName: string;
    baseUnit: string;
  };
}

export interface CreateMaterialRequest {
  materialName: string;
  baseUnit: string;
}

export interface CreateMaterialPriceRequest {
  materialId: string;
  unitPrice: number;
  currency: string;
  effectiveDate?: string;
}

// 订单相关类型
export interface SalesOrder {
  id: string;
  customerId: string;
  customerPoNumber?: string;
  totalAmount: number | string;
  orderStatus: "PENDING" | "CONFIRMED" | "SHIPPED" | "DELIVERED" | "CANCELLED";
  createdAt: string;
  updatedAt: string;
  customer?: Customer;
  salesOrderDetails?: SalesOrderDetail[];
  _count?: {
    salesOrderDetails: number;
  };
}

export interface SalesOrderDetail {
  id: string;
  orderId: string;
  materialId: string;
  quantity: number | string;
  unit: string;
  unitPrice: number | string;
  totalAmount: number | string;
  createdAt: string;
  updatedAt: string;
  material?: {
    materialName: string;
    baseUnit: string;
  };
}

export interface CreateSalesOrderRequest {
  customerId: string;
  customerPoNumber?: string;
  orderDetails: {
    materialId: string;
    quantity: number;
    unit: string;
    unitPrice: number;
  }[];
}

// 客户API
export const customerApi = {
  // 获取客户列表
  getCustomers: async (params?: PaginationParams & { search?: string }) => {
    const response = await api.get<
      ApiResponse<{
        customers: Customer[];
        pagination: PaginationResponse;
      }>
    >("/customers", { params });
    return response.data;
  },

  // 获取客户详情
  getCustomer: async (id: string) => {
    const response = await api.get<ApiResponse<Customer>>(`/customers/${id}`);
    return response.data;
  },

  // 创建客户
  createCustomer: async (data: CreateCustomerRequest) => {
    const response = await api.post<ApiResponse<Customer>>("/customers", data);
    return response.data;
  },

  // 更新客户
  updateCustomer: async (id: string, data: Partial<CreateCustomerRequest>) => {
    const response = await api.put<ApiResponse<Customer>>(
      `/customers/${id}`,
      data
    );
    return response.data;
  },

  // 删除客户
  deleteCustomer: async (id: string) => {
    const response = await api.delete<ApiResponse>(`/customers/${id}`);
    return response.data;
  },

  // 搜索客户
  searchCustomers: async (query: string, params?: PaginationParams) => {
    const response = await api.get<
      ApiResponse<{
        customers: Customer[];
        pagination: PaginationResponse;
      }>
    >("/customers/search", { params: { q: query, ...params } });
    return response.data;
  },
};

// 物料API
export const materialApi = {
  // 获取物料列表
  getMaterials: async (params?: PaginationParams & { search?: string }) => {
    const response = await api.get<
      ApiResponse<{
        materials: Material[];
        pagination: PaginationResponse;
      }>
    >("/materials", { params });
    return response.data;
  },

  // 获取物料详情
  getMaterial: async (id: string) => {
    const response = await api.get<ApiResponse<Material>>(`/materials/${id}`);
    return response.data;
  },

  // 创建物料
  createMaterial: async (data: CreateMaterialRequest) => {
    const response = await api.post<ApiResponse<Material>>("/materials", data);
    return response.data;
  },

  // 更新物料
  updateMaterial: async (id: string, data: Partial<CreateMaterialRequest>) => {
    const response = await api.put<ApiResponse<Material>>(
      `/materials/${id}`,
      data
    );
    return response.data;
  },

  // 删除物料
  deleteMaterial: async (id: string) => {
    const response = await api.delete<ApiResponse>(`/materials/${id}`);
    return response.data;
  },

  // 搜索物料
  searchMaterials: async (query: string, params?: PaginationParams) => {
    const response = await api.get<
      ApiResponse<{
        materials: Material[];
        pagination: PaginationResponse;
      }>
    >("/materials/search", { params: { q: query, ...params } });
    return response.data;
  },
};

// 价格API
export const priceApi = {
  // 获取所有物料的当前价格
  getAllCurrentPrices: async (
    params?: PaginationParams & { currency?: string }
  ) => {
    const response = await api.get<
      ApiResponse<{
        materials: Material[];
        pagination: PaginationResponse;
      }>
    >("/prices", { params });
    return response.data;
  },

  // 获取指定物料的价格历史
  getMaterialPrices: async (materialId: string, params?: PaginationParams) => {
    const response = await api.get<
      ApiResponse<{
        prices: MaterialPrice[];
        material: Material;
        pagination: PaginationResponse;
      }>
    >(`/prices/material/${materialId}`, { params });
    return response.data;
  },

  // 获取指定物料的当前价格
  getCurrentPrice: async (materialId: string) => {
    const response = await api.get<ApiResponse<MaterialPrice>>(
      `/prices/material/${materialId}/current`
    );
    return response.data;
  },

  // 创建物料价格
  createMaterialPrice: async (data: CreateMaterialPriceRequest) => {
    const response = await api.post<ApiResponse<MaterialPrice>>(
      "/prices",
      data
    );
    return response.data;
  },

  // 更新物料价格
  updateMaterialPrice: async (
    id: string,
    data: Partial<CreateMaterialPriceRequest>
  ) => {
    const response = await api.put<ApiResponse<MaterialPrice>>(
      `/prices/${id}`,
      data
    );
    return response.data;
  },

  // 删除物料价格
  deleteMaterialPrice: async (id: string) => {
    const response = await api.delete<ApiResponse>(`/prices/${id}`);
    return response.data;
  },
};

// 订单API
export const orderApi = {
  // 获取订单列表
  getOrders: async (
    params?: PaginationParams & { status?: string; customerId?: string }
  ) => {
    const response = await api.get<
      ApiResponse<{
        orders: SalesOrder[];
        pagination: PaginationResponse;
      }>
    >("/orders", { params });
    return response.data;
  },

  // 获取订单详情
  getOrder: async (id: string) => {
    const response = await api.get<ApiResponse<SalesOrder>>(`/orders/${id}`);
    return response.data;
  },

  // 创建订单
  createOrder: async (data: CreateSalesOrderRequest) => {
    const response = await api.post<ApiResponse<SalesOrder>>("/orders", data);
    return response.data;
  },

  // 更新订单
  updateOrder: async (id: string, data: Partial<CreateSalesOrderRequest>) => {
    const response = await api.put<ApiResponse<SalesOrder>>(
      `/orders/${id}`,
      data
    );
    return response.data;
  },

  // 删除订单
  deleteOrder: async (id: string) => {
    const response = await api.delete<ApiResponse>(`/orders/${id}`);
    return response.data;
  },

  // 搜索订单
  searchOrders: async (
    query: string,
    params?: PaginationParams & { status?: string }
  ) => {
    const response = await api.get<
      ApiResponse<{
        orders: SalesOrder[];
        pagination: PaginationResponse;
      }>
    >("/orders/search", { params: { q: query, ...params } });
    return response.data;
  },

  // 获取订单统计
  getOrderStatistics: async (params?: {
    startDate?: string;
    endDate?: string;
  }) => {
    const response = await api.get<
      ApiResponse<{
        summary: {
          totalOrders: number;
          totalAmount: number;
          ordersByStatus: Array<{
            orderStatus: string;
            _count: { id: number };
            _sum: { totalAmount: number };
          }>;
        };
        topCustomers: Array<Customer & { totalOrderAmount: number }>;
        topMaterials: Array<
          Material & { totalQuantity: number; totalAmount: number }
        >;
      }>
    >("/orders/statistics", { params });
    return response.data;
  },
};
