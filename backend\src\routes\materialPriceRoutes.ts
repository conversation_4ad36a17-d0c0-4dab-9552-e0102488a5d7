import { Router } from 'express';
import {
  getMaterialPrices,
  getCurrentPrice,
  createMaterialPrice,
  updateMaterialPrice,
  deleteMaterialPrice,
  getAllCurrentPrices,
} from '../controllers/materialPriceController';
import { validateRequired, validateMaterialPrice, validatePagination } from '../middleware/validation';

const router = Router();

// GET /api/prices - 获取所有物料的当前价格
router.get('/', validatePagination, getAllCurrentPrices);

// GET /api/prices/material/:materialId - 获取指定物料的所有价格记录
router.get('/material/:materialId', validatePagination, getMaterialPrices);

// GET /api/prices/material/:materialId/current - 获取指定物料的当前有效价格
router.get('/material/:materialId/current', getCurrentPrice);

// POST /api/prices - 创建新的物料价格
router.post(
  '/',
  validateRequired(['materialId', 'unitPrice', 'currency']),
  validateMaterialPrice,
  createMaterialPrice
);

// PUT /api/prices/:id - 更新物料价格
router.put('/:id', validateMaterialPrice, updateMaterialPrice);

// DELETE /api/prices/:id - 删除物料价格
router.delete('/:id', deleteMaterialPrice);

export default router;
