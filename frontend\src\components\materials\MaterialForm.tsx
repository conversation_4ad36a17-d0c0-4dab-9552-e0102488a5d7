"use client";

import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { materialApi, priceApi, Material } from "@/lib/api";
import { toast } from "sonner";

interface MaterialFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  material?: Material | null;
  onSuccess: () => void;
}

export default function MaterialForm({
  open,
  onOpenChange,
  material,
  onSuccess,
}: MaterialFormProps) {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    materialName: "",
    baseUnit: "",
    unitPrice: "",
    currency: "CNY",
  });

  // 当material变化时更新表单数据
  useEffect(() => {
    if (material) {
      setFormData({
        materialName: material.materialName,
        baseUnit: material.baseUnit,
        unitPrice: "",
        currency: "CNY",
      });
    } else {
      setFormData({
        materialName: "",
        baseUnit: "",
        unitPrice: "",
        currency: "CNY",
      });
    }
  }, [material]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.materialName.trim() || !formData.baseUnit.trim()) {
      toast.error("请填写物料名称和基本单位");
      return;
    }

    try {
      setLoading(true);

      if (material) {
        // 编辑物料
        const response = await materialApi.updateMaterial(material.id, {
          materialName: formData.materialName,
          baseUnit: formData.baseUnit,
        });

        if (response.success) {
          // 如果有价格信息，更新价格
          if (formData.unitPrice && parseFloat(formData.unitPrice) > 0) {
            await priceApi.createMaterialPrice({
              materialId: material.id,
              unitPrice: parseFloat(formData.unitPrice),
              currency: formData.currency,
            });
          }

          toast.success("物料更新成功");
          onSuccess();
          onOpenChange(false);
        }
      } else {
        // 新增物料
        const response = await materialApi.createMaterial({
          materialName: formData.materialName,
          baseUnit: formData.baseUnit,
        });

        if (response.success && response.data) {
          // 如果有价格信息，创建价格记录
          if (formData.unitPrice && parseFloat(formData.unitPrice) > 0) {
            await priceApi.createMaterialPrice({
              materialId: response.data.id,
              unitPrice: parseFloat(formData.unitPrice),
              currency: formData.currency,
            });
          }

          toast.success("物料创建成功");
          onSuccess();
          onOpenChange(false);
        }
      }
    } catch (error: any) {
      console.error("Failed to save material:", error);
      const errorMessage = error.response?.data?.message || "保存物料失败";
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{material ? "编辑物料" : "新增物料"}</DialogTitle>
          <DialogDescription>
            {material ? "修改物料信息和价格" : "添加新的物料信息和价格"}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="materialName">物料名称 *</Label>
            <Input
              id="materialName"
              value={formData.materialName}
              onChange={(e) =>
                handleInputChange("materialName", e.target.value)
              }
              placeholder="请输入物料名称"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="baseUnit">基本单位 *</Label>
            <Input
              id="baseUnit"
              value={formData.baseUnit}
              onChange={(e) => handleInputChange("baseUnit", e.target.value)}
              placeholder="如：个、吨、米等"
              required
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="unitPrice">单价</Label>
              <Input
                id="unitPrice"
                type="number"
                step="0.01"
                min="0"
                value={formData.unitPrice}
                onChange={(e) => handleInputChange("unitPrice", e.target.value)}
                placeholder="0.00"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="currency">币别</Label>
              <select
                id="currency"
                value={formData.currency}
                onChange={(e) => handleInputChange("currency", e.target.value)}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="CNY">人民币</option>
                <option value="USD">美元</option>
                <option value="EUR">欧元</option>
              </select>
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              取消
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? "保存中..." : material ? "更新" : "创建"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
