"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { priceApi, materialApi, Material, MaterialPrice, PaginationResponse } from "@/lib/api";
import { Pencil, Trash2, Plus, Search, DollarSign, History } from "lucide-react";
import { toast } from "sonner";
import PriceForm from "./PriceForm";
import PriceHistory from "./PriceHistory";

export default function PriceList() {
  const [materials, setMaterials] = useState<Material[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCurrency, setSelectedCurrency] = useState<string>("ALL");
  const [pagination, setPagination] = useState<PaginationResponse>({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0,
  });

  // 表单状态
  const [priceForm, setPriceForm] = useState<{
    open: boolean;
    price: MaterialPrice | null;
    materialId?: string;
  }>({
    open: false,
    price: null,
  });

  // 价格历史状态
  const [priceHistory, setPriceHistory] = useState<{
    open: boolean;
    material: Material | null;
  }>({
    open: false,
    material: null,
  });

  // 删除确认对话框状态
  const [deleteDialog, setDeleteDialog] = useState<{
    open: boolean;
    price: MaterialPrice | null;
  }>({
    open: false,
    price: null,
  });

  // 加载物料价格列表
  const loadMaterialPrices = async (page = 1, currency = "ALL") => {
    try {
      setLoading(true);
      const params: any = {
        page,
        limit: pagination.limit,
      };
      
      if (currency !== "ALL") {
        params.currency = currency;
      }

      const response = await priceApi.getAllCurrentPrices(params);

      if (response.success && response.data) {
        setMaterials(response.data.materials);
        setPagination(response.data.pagination);
      }
    } catch (error) {
      console.error("Failed to load material prices:", error);
      toast.error("加载价格列表失败");
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    loadMaterialPrices();
  }, []);

  // 货币筛选处理
  const handleCurrencyChange = (currency: string) => {
    setSelectedCurrency(currency);
    loadMaterialPrices(1, currency);
  };

  // 分页处理
  const handlePageChange = (newPage: number) => {
    loadMaterialPrices(newPage, selectedCurrency);
  };

  // 新增价格
  const handleAddPrice = (materialId?: string) => {
    setPriceForm({ open: true, price: null, materialId });
  };

  // 编辑价格
  const handleEditPrice = (material: Material) => {
    if (material.materialPrices && material.materialPrices.length > 0) {
      setPriceForm({ 
        open: true, 
        price: material.materialPrices[0],
        materialId: material.id 
      });
    } else {
      setPriceForm({ 
        open: true, 
        price: null,
        materialId: material.id 
      });
    }
  };

  // 查看价格历史
  const handleViewHistory = (material: Material) => {
    setPriceHistory({ open: true, material });
  };

  // 删除价格
  const handleDeletePrice = (material: Material) => {
    if (material.materialPrices && material.materialPrices.length > 0) {
      setDeleteDialog({ open: true, price: material.materialPrices[0] });
    }
  };

  // 确认删除
  const confirmDelete = async () => {
    if (!deleteDialog.price) return;

    try {
      const response = await priceApi.deleteMaterialPrice(deleteDialog.price.id);
      if (response.success) {
        toast.success("价格删除成功");
        loadMaterialPrices(pagination.page, selectedCurrency);
      }
    } catch (error: any) {
      console.error("Failed to delete price:", error);
      const errorMessage = error.response?.data?.message || "删除价格失败";
      toast.error(errorMessage);
    } finally {
      setDeleteDialog({ open: false, price: null });
    }
  };

  // 表单成功回调
  const handleFormSuccess = () => {
    setPriceForm({ open: false, price: null });
    loadMaterialPrices(pagination.page, selectedCurrency);
  };

  // 格式化价格
  const formatPrice = (material: Material) => {
    if (material.materialPrices && material.materialPrices.length > 0) {
      const latestPrice = material.materialPrices[0];
      return {
        price: `${latestPrice.currency} ${Number(latestPrice.unitPrice).toFixed(2)}`,
        date: new Date(latestPrice.effectiveDate).toLocaleDateString(),
        hasPrice: true,
      };
    }
    return {
      price: "未设置价格",
      date: "-",
      hasPrice: false,
    };
  };

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">价格管理</h1>
          <p className="text-muted-foreground">
            管理物料价格信息，包括价格设置、价格历史等
          </p>
        </div>
        <Button onClick={() => handleAddPrice()} size="lg">
          <Plus className="mr-2 h-4 w-4" />
          新增价格
        </Button>
      </div>

      {/* 筛选栏 */}
      <Card>
        <CardHeader>
          <CardTitle>筛选条件</CardTitle>
          <CardDescription>根据货币类型筛选价格信息</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="w-48">
              <Select value={selectedCurrency} onValueChange={handleCurrencyChange}>
                <SelectTrigger>
                  <SelectValue placeholder="选择货币" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">全部货币</SelectItem>
                  <SelectItem value="CNY">人民币 (CNY)</SelectItem>
                  <SelectItem value="USD">美元 (USD)</SelectItem>
                  <SelectItem value="EUR">欧元 (EUR)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 价格列表 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            物料价格列表
          </CardTitle>
          <CardDescription>
            共 {pagination.total} 个物料，第 {pagination.page} 页 / 共{" "}
            {pagination.pages} 页
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <p className="mt-2 text-muted-foreground">加载中...</p>
            </div>
          ) : materials.length === 0 ? (
            <div className="text-center py-8">
              <DollarSign className="mx-auto h-12 w-12 text-muted-foreground" />
              <p className="mt-2 text-muted-foreground">暂无价格数据</p>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>物料名称</TableHead>
                    <TableHead>基本单位</TableHead>
                    <TableHead>当前价格</TableHead>
                    <TableHead>生效日期</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {materials.map((material) => {
                    const priceInfo = formatPrice(material);
                    return (
                      <TableRow key={material.id}>
                        <TableCell className="font-medium">
                          {material.materialName}
                        </TableCell>
                        <TableCell>{material.baseUnit}</TableCell>
                        <TableCell>
                          <span className={priceInfo.hasPrice ? "" : "text-muted-foreground"}>
                            {priceInfo.price}
                          </span>
                        </TableCell>
                        <TableCell>{priceInfo.date}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleViewHistory(material)}
                            >
                              <History className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEditPrice(material)}
                            >
                              <Pencil className="h-4 w-4" />
                            </Button>
                            {priceInfo.hasPrice && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDeletePrice(material)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>

              {/* 分页控件 */}
              {pagination.pages > 1 && (
                <div className="flex justify-center gap-2 mt-4">
                  <Button
                    variant="outline"
                    disabled={pagination.page <= 1}
                    onClick={() => handlePageChange(pagination.page - 1)}
                  >
                    上一页
                  </Button>
                  <span className="flex items-center px-4">
                    第 {pagination.page} 页 / 共 {pagination.pages} 页
                  </span>
                  <Button
                    variant="outline"
                    disabled={pagination.page >= pagination.pages}
                    onClick={() => handlePageChange(pagination.page + 1)}
                  >
                    下一页
                  </Button>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* 价格表单对话框 */}
      <PriceForm
        open={priceForm.open}
        onOpenChange={(open: boolean) =>
          setPriceForm({ open, price: null })
        }
        price={priceForm.price}
        materialId={priceForm.materialId}
        onSuccess={handleFormSuccess}
      />

      {/* 价格历史对话框 */}
      <PriceHistory
        open={priceHistory.open}
        onOpenChange={(open: boolean) =>
          setPriceHistory({ open, material: null })
        }
        material={priceHistory.material}
      />

      {/* 删除确认对话框 */}
      <AlertDialog
        open={deleteDialog.open}
        onOpenChange={(open) =>
          setDeleteDialog({ open, price: deleteDialog.price })
        }
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除这个价格记录吗？此操作不可撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete}>删除</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
