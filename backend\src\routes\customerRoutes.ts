import { Router } from 'express';
import {
  getCustomers,
  getCustomerById,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  searchCustomers,
} from '../controllers/customerController';
import { validateRequired, validateCustomer, validatePagination } from '../middleware/validation';
import { asyncHandler } from '../middleware/errorHandler';

const router = Router();

// GET /api/customers - 获取所有客户（分页）
router.get('/', validatePagination, getCustomers);

// GET /api/customers/search - 搜索客户
router.get('/search', validatePagination, searchCustomers);

// GET /api/customers/:id - 根据ID获取客户
router.get('/:id', getCustomerById);

// POST /api/customers - 创建新客户
router.post(
  '/',
  validateRequired(['customerName', 'contactPerson', 'address']),
  validateCustomer,
  createCustomer
);

// PUT /api/customers/:id - 更新客户
router.put('/:id', validateCustomer, updateCustomer);

// DELETE /api/customers/:id - 删除客户
router.delete('/:id', deleteCustomer);

export default router;
