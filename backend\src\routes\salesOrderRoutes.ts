import { Router } from 'express';
import {
  getSalesOrders,
  getSalesOrderById,
  createSalesOrder,
  updateSalesOrder,
  deleteSalesOrder,
  searchSalesOrders,
  getOrderStatistics,
} from '../controllers/salesOrderController';
import { validateRequired, validateSalesOrder, validatePagination } from '../middleware/validation';

const router = Router();

// GET /api/orders - 获取所有订单（分页）
router.get('/', validatePagination, getSalesOrders);

// GET /api/orders/search - 搜索订单
router.get('/search', validatePagination, searchSalesOrders);

// GET /api/orders/statistics - 获取订单统计信息
router.get('/statistics', getOrderStatistics);

// GET /api/orders/:id - 根据ID获取订单详情
router.get('/:id', getSalesOrderById);

// POST /api/orders - 创建新订单
router.post(
  '/',
  validateRequired(['customerId', 'orderDetails']),
  validateSalesOrder,
  createSalesOrder
);

// PUT /api/orders/:id - 更新订单
router.put('/:id', updateSalesOrder);

// DELETE /api/orders/:id - 删除订单
router.delete('/:id', deleteSalesOrder);

export default router;
