import { Request, Response } from 'express';
import prisma from '../lib/prisma';
import { ApiResponse, CreateMaterialPriceRequest, UpdateMaterialPriceRequest } from '../types';
import { AppError, asyncHandler } from '../middleware/errorHandler';

// 获取物料的所有价格记录
export const getMaterialPrices = asyncHandler(async (req: Request, res: Response) => {
  const { materialId } = req.params;
  const { page = 1, limit = 10, sortBy = 'effectiveDate', sortOrder = 'desc' } = req.query as any;
  
  const skip = (Number(page) - 1) * Number(limit);
  
  // 验证物料是否存在
  const material = await prisma.material.findUnique({
    where: { id: materialId },
  });

  if (!material) {
    throw new AppError('Material not found', 404);
  }

  const [prices, total] = await Promise.all([
    prisma.materialPrice.findMany({
      where: { materialId },
      skip,
      take: Number(limit),
      orderBy: {
        [sortBy]: sortOrder,
      },
      include: {
        material: {
          select: {
            materialName: true,
            baseUnit: true,
          },
        },
      },
    }),
    prisma.materialPrice.count({
      where: { materialId },
    }),
  ]);

  const response: ApiResponse = {
    success: true,
    message: 'Material prices retrieved successfully',
    data: {
      prices,
      material,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit)),
      },
    },
  };

  res.json(response);
});

// 获取物料的当前有效价格
export const getCurrentPrice = asyncHandler(async (req: Request, res: Response) => {
  const { materialId } = req.params;

  const currentPrice = await prisma.materialPrice.findFirst({
    where: { 
      materialId,
      effectiveDate: {
        lte: new Date(),
      },
    },
    orderBy: {
      effectiveDate: 'desc',
    },
    include: {
      material: {
        select: {
          materialName: true,
          baseUnit: true,
        },
      },
    },
  });

  if (!currentPrice) {
    throw new AppError('No current price found for this material', 404);
  }

  const response: ApiResponse = {
    success: true,
    message: 'Current price retrieved successfully',
    data: currentPrice,
  };

  res.json(response);
});

// 创建新的物料价格
export const createMaterialPrice = asyncHandler(async (req: Request, res: Response) => {
  const { materialId, unitPrice, currency, effectiveDate }: CreateMaterialPriceRequest = req.body;

  // 验证物料是否存在
  const material = await prisma.material.findUnique({
    where: { id: materialId },
  });

  if (!material) {
    throw new AppError('Material not found', 404);
  }

  const price = await prisma.materialPrice.create({
    data: {
      materialId,
      unitPrice,
      currency,
      effectiveDate: effectiveDate ? new Date(effectiveDate) : new Date(),
    },
    include: {
      material: {
        select: {
          materialName: true,
          baseUnit: true,
        },
      },
    },
  });

  const response: ApiResponse = {
    success: true,
    message: 'Material price created successfully',
    data: price,
  };

  res.status(201).json(response);
});

// 更新物料价格
export const updateMaterialPrice = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const updateData: UpdateMaterialPriceRequest = req.body;

  // 检查价格记录是否存在
  const existingPrice = await prisma.materialPrice.findUnique({
    where: { id },
  });

  if (!existingPrice) {
    throw new AppError('Material price not found', 404);
  }

  // 如果更新了materialId，验证新物料是否存在
  if (updateData.materialId) {
    const material = await prisma.material.findUnique({
      where: { id: updateData.materialId },
    });

    if (!material) {
      throw new AppError('Material not found', 404);
    }
  }

  const price = await prisma.materialPrice.update({
    where: { id },
    data: {
      ...updateData,
      effectiveDate: updateData.effectiveDate ? new Date(updateData.effectiveDate) : undefined,
    },
    include: {
      material: {
        select: {
          materialName: true,
          baseUnit: true,
        },
      },
    },
  });

  const response: ApiResponse = {
    success: true,
    message: 'Material price updated successfully',
    data: price,
  };

  res.json(response);
});

// 删除物料价格
export const deleteMaterialPrice = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  // 检查价格记录是否存在
  const existingPrice = await prisma.materialPrice.findUnique({
    where: { id },
  });

  if (!existingPrice) {
    throw new AppError('Material price not found', 404);
  }

  await prisma.materialPrice.delete({
    where: { id },
  });

  const response: ApiResponse = {
    success: true,
    message: 'Material price deleted successfully',
  };

  res.json(response);
});

// 获取所有物料的最新价格
export const getAllCurrentPrices = asyncHandler(async (req: Request, res: Response) => {
  const { page = 1, limit = 10, currency } = req.query as any;
  
  const skip = (Number(page) - 1) * Number(limit);

  // 构建查询条件
  const whereCondition = currency ? { currency } : {};

  // 获取每个物料的最新价格
  const materials = await prisma.material.findMany({
    skip,
    take: Number(limit),
    include: {
      materialPrices: {
        where: whereCondition,
        orderBy: {
          effectiveDate: 'desc',
        },
        take: 1,
      },
    },
    orderBy: {
      materialName: 'asc',
    },
  });

  const total = await prisma.material.count();

  const response: ApiResponse = {
    success: true,
    message: 'Current prices retrieved successfully',
    data: {
      materials,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit)),
      },
    },
  };

  res.json(response);
});
