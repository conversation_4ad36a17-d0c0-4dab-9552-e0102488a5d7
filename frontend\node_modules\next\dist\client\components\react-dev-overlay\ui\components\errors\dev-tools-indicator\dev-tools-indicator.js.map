{"version": 3, "sources": ["../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-indicator.tsx"], "sourcesContent": ["import type { CSSProperties, Dispatch, SetStateAction } from 'react'\nimport type { OverlayState } from '../../../../shared'\n\nimport { useState, useEffect, useRef, createContext, useContext } from 'react'\nimport { Toast } from '../../toast'\nimport { NextLogo } from './next-logo'\nimport { useIsDevBuilding } from '../../../../../../dev/dev-build-indicator/internal/initialize'\nimport { useIsDevRendering } from '../../../../utils/dev-indicator/dev-render-indicator'\nimport { useDelayedRender } from '../../../hooks/use-delayed-render'\nimport { TurbopackInfo } from './dev-tools-info/turbopack-info'\nimport { RouteInfo } from './dev-tools-info/route-info'\nimport GearIcon from '../../../icons/gear-icon'\nimport { UserPreferences } from './dev-tools-info/user-preferences'\nimport {\n  MENU_CURVE,\n  MENU_DURATION_MS,\n  useClickOutside,\n  useFocusTrap,\n} from './utils'\nimport {\n  getInitialPosition,\n  type DevToolsScale,\n} from './dev-tools-info/preferences'\n\n// TODO: add E2E tests to cover different scenarios\n\nexport function DevToolsIndicator({\n  state,\n  errorCount,\n  isBuildError,\n  setIsErrorOverlayOpen,\n  ...props\n}: {\n  state: OverlayState\n  errorCount: number\n  isBuildError: boolean\n  setIsErrorOverlayOpen: (\n    isErrorOverlayOpen: boolean | ((prev: boolean) => boolean)\n  ) => void\n  scale: DevToolsScale\n  setScale: (value: DevToolsScale) => void\n}) {\n  const [isDevToolsIndicatorVisible, setIsDevToolsIndicatorVisible] =\n    useState(true)\n\n  return (\n    <DevToolsPopover\n      routerType={state.routerType}\n      semver={state.versionInfo.installed}\n      issueCount={errorCount}\n      isStaticRoute={state.staticIndicator}\n      hide={() => {\n        setIsDevToolsIndicatorVisible(false)\n        fetch('/__nextjs_disable_dev_indicator', {\n          method: 'POST',\n        })\n      }}\n      setIsErrorOverlayOpen={setIsErrorOverlayOpen}\n      isTurbopack={!!process.env.TURBOPACK}\n      disabled={state.disableDevIndicator || !isDevToolsIndicatorVisible}\n      isBuildError={isBuildError}\n      {...props}\n    />\n  )\n}\n\n//////////////////////////////////////////////////////////////////////////////////////\n\ninterface C {\n  closeMenu: () => void\n  selectedIndex: number\n  setSelectedIndex: Dispatch<SetStateAction<number>>\n}\n\nconst Context = createContext({} as C)\n\nconst OVERLAYS = {\n  Root: 'root',\n  Turbo: 'turbo',\n  Route: 'route',\n  Preferences: 'preferences',\n} as const\n\nexport type Overlays = (typeof OVERLAYS)[keyof typeof OVERLAYS]\n\nfunction DevToolsPopover({\n  routerType,\n  disabled,\n  issueCount,\n  isStaticRoute,\n  isTurbopack,\n  isBuildError,\n  hide,\n  setIsErrorOverlayOpen,\n  scale,\n  setScale,\n}: {\n  routerType: 'pages' | 'app'\n  disabled: boolean\n  issueCount: number\n  isStaticRoute: boolean\n  semver: string | undefined\n  isTurbopack: boolean\n  isBuildError: boolean\n  hide: () => void\n  setIsErrorOverlayOpen: (\n    isOverlayOpen: boolean | ((prev: boolean) => boolean)\n  ) => void\n  scale: DevToolsScale\n  setScale: (value: DevToolsScale) => void\n}) {\n  const menuRef = useRef<HTMLDivElement>(null)\n  const triggerRef = useRef<HTMLButtonElement | null>(null)\n\n  const [open, setOpen] = useState<Overlays | null>(null)\n  const [position, setPosition] = useState(getInitialPosition())\n  const [selectedIndex, setSelectedIndex] = useState(-1)\n\n  const isMenuOpen = open === OVERLAYS.Root\n  const isTurbopackInfoOpen = open === OVERLAYS.Turbo\n  const isRouteInfoOpen = open === OVERLAYS.Route\n  const isPreferencesOpen = open === OVERLAYS.Preferences\n\n  const { mounted: menuMounted, rendered: menuRendered } = useDelayedRender(\n    isMenuOpen,\n    {\n      // Intentionally no fade in, makes the UI feel more immediate\n      enterDelay: 0,\n      // Graceful fade out to confirm that the UI did not break\n      exitDelay: MENU_DURATION_MS,\n    }\n  )\n\n  // Features to make the menu accessible\n  useFocusTrap(menuRef, triggerRef, isMenuOpen)\n  useClickOutside(menuRef, triggerRef, isMenuOpen, closeMenu)\n\n  useEffect(() => {\n    if (open === null) {\n      // Avoid flashing selected state\n      const id = setTimeout(() => {\n        setSelectedIndex(-1)\n      }, MENU_DURATION_MS)\n      return () => clearTimeout(id)\n    }\n  }, [open])\n\n  function select(index: number | 'first' | 'last') {\n    if (index === 'first') {\n      setTimeout(() => {\n        const all = menuRef.current?.querySelectorAll('[role=\"menuitem\"]')\n        if (all) {\n          const firstIndex = all[0].getAttribute('data-index')\n          select(Number(firstIndex))\n        }\n      })\n      return\n    }\n\n    if (index === 'last') {\n      setTimeout(() => {\n        const all = menuRef.current?.querySelectorAll('[role=\"menuitem\"]')\n        if (all) {\n          const lastIndex = all.length - 1\n          select(lastIndex)\n        }\n      })\n      return\n    }\n\n    const el = menuRef.current?.querySelector(\n      `[data-index=\"${index}\"]`\n    ) as HTMLElement\n\n    if (el) {\n      setSelectedIndex(index)\n      el?.focus()\n    }\n  }\n\n  function onMenuKeydown(e: React.KeyboardEvent<HTMLDivElement>) {\n    e.preventDefault()\n\n    switch (e.key) {\n      case 'ArrowDown':\n        const next = selectedIndex + 1\n        select(next)\n        break\n      case 'ArrowUp':\n        const prev = selectedIndex - 1\n        select(prev)\n        break\n      case 'Home':\n        select('first')\n        break\n      case 'End':\n        select('last')\n        break\n      default:\n        break\n    }\n  }\n\n  function openErrorOverlay() {\n    setOpen(null)\n    if (issueCount > 0) {\n      setIsErrorOverlayOpen(true)\n    }\n  }\n\n  function toggleErrorOverlay() {\n    setIsErrorOverlayOpen((prev) => !prev)\n  }\n\n  function openRootMenu() {\n    setOpen((prevOpen) => {\n      if (prevOpen === null) select('first')\n      return OVERLAYS.Root\n    })\n  }\n\n  function onTriggerClick() {\n    if (open === OVERLAYS.Root) {\n      setOpen(null)\n    } else {\n      openRootMenu()\n      setTimeout(() => {\n        select('first')\n      })\n    }\n  }\n\n  function closeMenu() {\n    // Only close when we were on `Root`,\n    // otherwise it will close other overlays\n    setOpen((prevOpen) => {\n      if (prevOpen === OVERLAYS.Root) {\n        return null\n      }\n      return prevOpen\n    })\n  }\n\n  function handleHideDevtools() {\n    setOpen(null)\n    hide()\n  }\n\n  const [vertical, horizontal] = position.split('-', 2)\n  const popover = { [vertical]: 'calc(100% + 8px)', [horizontal]: 0 }\n\n  return (\n    <Toast\n      data-nextjs-toast\n      style={\n        {\n          '--animate-out-duration-ms': `${MENU_DURATION_MS}ms`,\n          '--animate-out-timing-function': MENU_CURVE,\n          boxShadow: 'none',\n          zIndex: 2147483647,\n          // Reset the toast component's default positions.\n          bottom: 'initial',\n          left: 'initial',\n          [vertical]: '20px',\n          [horizontal]: '20px',\n        } as CSSProperties\n      }\n    >\n      {/* Trigger */}\n      <NextLogo\n        ref={triggerRef}\n        aria-haspopup=\"menu\"\n        aria-expanded={isMenuOpen}\n        aria-controls=\"nextjs-dev-tools-menu\"\n        aria-label={`${isMenuOpen ? 'Close' : 'Open'} Next.js Dev Tools`}\n        data-nextjs-dev-tools-button\n        disabled={disabled}\n        issueCount={issueCount}\n        onTriggerClick={onTriggerClick}\n        toggleErrorOverlay={toggleErrorOverlay}\n        isDevBuilding={useIsDevBuilding()}\n        isDevRendering={useIsDevRendering()}\n        isBuildError={isBuildError}\n        scale={scale}\n      />\n\n      {/* Route Info */}\n      <RouteInfo\n        isOpen={isRouteInfoOpen}\n        close={openRootMenu}\n        triggerRef={triggerRef}\n        style={popover}\n        routerType={routerType}\n        routeType={isStaticRoute ? 'Static' : 'Dynamic'}\n      />\n\n      {/* Turbopack Info */}\n      <TurbopackInfo\n        isOpen={isTurbopackInfoOpen}\n        close={openRootMenu}\n        triggerRef={triggerRef}\n        style={popover}\n      />\n\n      {/* Preferences */}\n      <UserPreferences\n        isOpen={isPreferencesOpen}\n        close={openRootMenu}\n        triggerRef={triggerRef}\n        style={popover}\n        hide={handleHideDevtools}\n        setPosition={setPosition}\n        position={position}\n        scale={scale}\n        setScale={setScale}\n      />\n\n      {/* Dropdown Menu */}\n      {menuMounted && (\n        <div\n          ref={menuRef}\n          id=\"nextjs-dev-tools-menu\"\n          role=\"menu\"\n          dir=\"ltr\"\n          aria-orientation=\"vertical\"\n          aria-label=\"Next.js Dev Tools Items\"\n          tabIndex={-1}\n          className=\"dev-tools-indicator-menu\"\n          onKeyDown={onMenuKeydown}\n          data-rendered={menuRendered}\n          style={popover}\n        >\n          <Context.Provider\n            value={{\n              closeMenu,\n              selectedIndex,\n              setSelectedIndex,\n            }}\n          >\n            <div className=\"dev-tools-indicator-inner\">\n              {issueCount > 0 && (\n                <MenuItem\n                  title={`${issueCount} ${issueCount === 1 ? 'issue' : 'issues'} found. Click to view details in the dev overlay.`}\n                  index={0}\n                  label=\"Issues\"\n                  value={<IssueCount>{issueCount}</IssueCount>}\n                  onClick={openErrorOverlay}\n                />\n              )}\n              <MenuItem\n                title={`Current route is ${isStaticRoute ? 'static' : 'dynamic'}.`}\n                label=\"Route\"\n                index={1}\n                value={isStaticRoute ? 'Static' : 'Dynamic'}\n                onClick={() => setOpen(OVERLAYS.Route)}\n                data-nextjs-route-type={isStaticRoute ? 'static' : 'dynamic'}\n              />\n              {isTurbopack ? (\n                <MenuItem\n                  title=\"Turbopack is enabled.\"\n                  label=\"Turbopack\"\n                  value=\"Enabled\"\n                />\n              ) : (\n                <MenuItem\n                  index={2}\n                  title=\"Learn about Turbopack and how to enable it in your application.\"\n                  label=\"Try Turbopack\"\n                  value={<ChevronRight />}\n                  onClick={() => setOpen(OVERLAYS.Turbo)}\n                />\n              )}\n            </div>\n\n            <div className=\"dev-tools-indicator-footer\">\n              <MenuItem\n                data-preferences\n                label=\"Preferences\"\n                value={<GearIcon />}\n                onClick={() => setOpen(OVERLAYS.Preferences)}\n                index={isTurbopack ? 2 : 3}\n              />\n            </div>\n          </Context.Provider>\n        </div>\n      )}\n    </Toast>\n  )\n}\n\nfunction ChevronRight() {\n  return (\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      width=\"16\"\n      height=\"16\"\n      viewBox=\"0 0 16 16\"\n      fill=\"none\"\n    >\n      <path\n        fill=\"#666\"\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M5.50011 1.93945L6.03044 2.46978L10.8537 7.293C11.2442 7.68353 11.2442 8.31669 10.8537 8.70722L6.03044 13.5304L5.50011 14.0608L4.43945 13.0001L4.96978 12.4698L9.43945 8.00011L4.96978 3.53044L4.43945 3.00011L5.50011 1.93945Z\"\n      />\n    </svg>\n  )\n}\n\nfunction MenuItem({\n  index,\n  label,\n  value,\n  onClick,\n  href,\n  ...props\n}: {\n  index?: number\n  title?: string\n  label: string\n  value: React.ReactNode\n  href?: string\n  onClick?: () => void\n}) {\n  const isInteractive =\n    typeof onClick === 'function' || typeof href === 'string'\n  const { closeMenu, selectedIndex, setSelectedIndex } = useContext(Context)\n  const selected = selectedIndex === index\n\n  function click() {\n    if (isInteractive) {\n      onClick?.()\n      closeMenu()\n      if (href) {\n        window.open(href, '_blank', 'noopener, noreferrer')\n      }\n    }\n  }\n\n  return (\n    <div\n      className=\"dev-tools-indicator-item\"\n      data-index={index}\n      data-selected={selected}\n      onClick={click}\n      // Needs `onMouseMove` instead of enter to work together\n      // with keyboard and mouse input\n      onMouseMove={() => {\n        if (isInteractive && index !== undefined && selectedIndex !== index) {\n          setSelectedIndex(index)\n        }\n      }}\n      onMouseLeave={() => setSelectedIndex(-1)}\n      onKeyDown={(e) => {\n        if (e.key === 'Enter' || e.key === ' ') {\n          click()\n        }\n      }}\n      role={isInteractive ? 'menuitem' : undefined}\n      tabIndex={selected ? 0 : -1}\n      {...props}\n    >\n      <span className=\"dev-tools-indicator-label\">{label}</span>\n      <span className=\"dev-tools-indicator-value\">{value}</span>\n    </div>\n  )\n}\n\nfunction IssueCount({ children }: { children: number }) {\n  return (\n    <span\n      className=\"dev-tools-indicator-issue-count\"\n      data-has-issues={children > 0}\n    >\n      <span className=\"dev-tools-indicator-issue-count-indicator\" />\n      {children}\n    </span>\n  )\n}\n\n//////////////////////////////////////////////////////////////////////////////////////\n\nexport const DEV_TOOLS_INDICATOR_STYLES = `\n  .dev-tools-indicator-menu {\n    -webkit-font-smoothing: antialiased;\n    display: flex;\n    flex-direction: column;\n    align-items: flex-start;\n    background: var(--color-background-100);\n    border: 1px solid var(--color-gray-alpha-400);\n    background-clip: padding-box;\n    box-shadow: var(--shadow-menu);\n    border-radius: var(--rounded-xl);\n    position: absolute;\n    font-family: var(--font-stack-sans);\n    z-index: 1000;\n    overflow: hidden;\n    opacity: 0;\n    outline: 0;\n    min-width: 248px;\n    transition: opacity var(--animate-out-duration-ms)\n      var(--animate-out-timing-function);\n\n    &[data-rendered='true'] {\n      opacity: 1;\n      scale: 1;\n    }\n  }\n\n  .dev-tools-indicator-inner {\n    padding: 6px;\n    width: 100%;\n  }\n\n  .dev-tools-indicator-item {\n    display: flex;\n    align-items: center;\n    padding: 8px 6px;\n    height: var(--size-36);\n    border-radius: 6px;\n    text-decoration: none !important;\n    user-select: none;\n    white-space: nowrap;\n\n    svg {\n      width: var(--size-16);\n      height: var(--size-16);\n    }\n\n    &:focus-visible {\n      outline: 0;\n    }\n  }\n\n  .dev-tools-indicator-footer {\n    background: var(--color-background-200);\n    padding: 6px;\n    border-top: 1px solid var(--color-gray-400);\n    width: 100%;\n  }\n\n  .dev-tools-indicator-item[data-selected='true'] {\n    cursor: pointer;\n    background-color: var(--color-gray-200);\n  }\n\n  .dev-tools-indicator-label {\n    font-size: var(--size-14);\n    line-height: var(--size-20);\n    color: var(--color-gray-1000);\n  }\n\n  .dev-tools-indicator-value {\n    font-size: var(--size-14);\n    line-height: var(--size-20);\n    color: var(--color-gray-900);\n    margin-left: auto;\n  }\n\n  .dev-tools-indicator-issue-count {\n    --color-primary: var(--color-gray-800);\n    --color-secondary: var(--color-gray-100);\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n    justify-content: center;\n    gap: 8px;\n    min-width: var(--size-40);\n    height: var(--size-24);\n    background: var(--color-background-100);\n    border: 1px solid var(--color-gray-alpha-400);\n    background-clip: padding-box;\n    box-shadow: var(--shadow-small);\n    padding: 2px;\n    color: var(--color-gray-1000);\n    border-radius: 128px;\n    font-weight: 500;\n    font-size: var(--size-13);\n    font-variant-numeric: tabular-nums;\n\n    &[data-has-issues='true'] {\n      --color-primary: var(--color-red-800);\n      --color-secondary: var(--color-red-100);\n    }\n\n    .dev-tools-indicator-issue-count-indicator {\n      width: var(--size-8);\n      height: var(--size-8);\n      background: var(--color-primary);\n      box-shadow: 0 0 0 2px var(--color-secondary);\n      border-radius: 50%;\n    }\n  }\n\n  .dev-tools-indicator-shortcut {\n    display: flex;\n    gap: 4px;\n\n    kbd {\n      width: var(--size-20);\n      height: var(--size-20);\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      border-radius: var(--rounded-md);\n      border: 1px solid var(--color-gray-400);\n      font-family: var(--font-stack-sans);\n      background: var(--color-background-100);\n      color: var(--color-gray-1000);\n      text-align: center;\n      font-size: var(--size-12);\n      line-height: var(--size-16);\n    }\n  }\n`\n"], "names": ["DEV_TOOLS_INDICATOR_STYLES", "DevToolsIndicator", "state", "errorCount", "isBuildError", "setIsErrorOverlayOpen", "props", "isDevToolsIndicatorVisible", "setIsDevToolsIndicatorVisible", "useState", "DevToolsPopover", "routerType", "semver", "versionInfo", "installed", "issueCount", "isStaticRoute", "staticIndicator", "hide", "fetch", "method", "isTurbopack", "process", "env", "TURBOPACK", "disabled", "disableDevIndicator", "Context", "createContext", "OVERLAYS", "Root", "Turbo", "Route", "Preferences", "scale", "setScale", "menuRef", "useRef", "triggerRef", "open", "<PERSON><PERSON><PERSON>", "position", "setPosition", "getInitialPosition", "selectedIndex", "setSelectedIndex", "isMenuOpen", "isTurbopackInfoOpen", "isRouteInfoOpen", "isPreferencesOpen", "mounted", "menuMounted", "rendered", "menuRendered", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enterDelay", "exitDelay", "MENU_DURATION_MS", "useFocusTrap", "useClickOutside", "closeMenu", "useEffect", "id", "setTimeout", "clearTimeout", "select", "index", "all", "current", "querySelectorAll", "firstIndex", "getAttribute", "Number", "lastIndex", "length", "el", "querySelector", "focus", "onMenuKeydown", "e", "preventDefault", "key", "next", "prev", "openErrorOverlay", "toggleError<PERSON><PERSON>lay", "openRootMenu", "prevOpen", "onTriggerClick", "handleHideDevtools", "vertical", "horizontal", "split", "popover", "Toast", "data-nextjs-toast", "style", "MENU_CURVE", "boxShadow", "zIndex", "bottom", "left", "NextLogo", "ref", "aria-haspopup", "aria-expanded", "aria-controls", "aria-label", "data-nextjs-dev-tools-button", "isDevBuilding", "useIsDevBuilding", "isDevRendering", "useIsDevRendering", "RouteInfo", "isOpen", "close", "routeType", "TurbopackInfo", "UserPreferences", "div", "role", "dir", "aria-orientation", "tabIndex", "className", "onKeyDown", "data-rendered", "Provider", "value", "MenuItem", "title", "label", "IssueCount", "onClick", "data-nextjs-route-type", "ChevronRight", "data-preferences", "GearIcon", "svg", "xmlns", "width", "height", "viewBox", "fill", "path", "fillRule", "clipRule", "d", "href", "isInteractive", "useContext", "selected", "click", "window", "data-index", "data-selected", "onMouseMove", "undefined", "onMouseLeave", "span", "children", "data-has-issues"], "mappings": ";;;;;;;;;;;;;;;IAkeaA,0BAA0B;eAA1BA;;IAxcGC,iBAAiB;eAAjBA;;;;;uBAvBuD;uBACjD;0BACG;4BACQ;oCACC;kCACD;+BACH;2BACJ;mEACL;iCACW;uBAMzB;6BAIA;AAIA,SAASA,kBAAkB,KAejC;IAfiC,IAAA,EAChCC,KAAK,EACLC,UAAU,EACVC,YAAY,EACZC,qBAAqB,EACrB,GAAGC,OAUJ,GAfiC;IAgBhC,MAAM,CAACC,4BAA4BC,8BAA8B,GAC/DC,IAAAA,eAAQ,EAAC;IAEX,qBACE,qBAACC;QACCC,YAAYT,MAAMS,UAAU;QAC5BC,QAAQV,MAAMW,WAAW,CAACC,SAAS;QACnCC,YAAYZ;QACZa,eAAed,MAAMe,eAAe;QACpCC,MAAM;YACJV,8BAA8B;YAC9BW,MAAM,mCAAmC;gBACvCC,QAAQ;YACV;QACF;QACAf,uBAAuBA;QACvBgB,aAAa,CAAC,CAACC,QAAQC,GAAG,CAACC,SAAS;QACpCC,UAAUvB,MAAMwB,mBAAmB,IAAI,CAACnB;QACxCH,cAAcA;QACb,GAAGE,KAAK;;AAGf;AAUA,MAAMqB,wBAAUC,IAAAA,oBAAa,EAAC,CAAC;AAE/B,MAAMC,WAAW;IACfC,MAAM;IACNC,OAAO;IACPC,OAAO;IACPC,aAAa;AACf;AAIA,SAASvB,gBAAgB,KAyBxB;IAzBwB,IAAA,EACvBC,UAAU,EACVc,QAAQ,EACRV,UAAU,EACVC,aAAa,EACbK,WAAW,EACXjB,YAAY,EACZc,IAAI,EACJb,qBAAqB,EACrB6B,KAAK,EACLC,QAAQ,EAeT,GAzBwB;IA0BvB,MAAMC,UAAUC,IAAAA,aAAM,EAAiB;IACvC,MAAMC,aAAaD,IAAAA,aAAM,EAA2B;IAEpD,MAAM,CAACE,MAAMC,QAAQ,GAAG/B,IAAAA,eAAQ,EAAkB;IAClD,MAAM,CAACgC,UAAUC,YAAY,GAAGjC,IAAAA,eAAQ,EAACkC,IAAAA,+BAAkB;IAC3D,MAAM,CAACC,eAAeC,iBAAiB,GAAGpC,IAAAA,eAAQ,EAAC,CAAC;IAEpD,MAAMqC,aAAaP,SAASV,SAASC,IAAI;IACzC,MAAMiB,sBAAsBR,SAASV,SAASE,KAAK;IACnD,MAAMiB,kBAAkBT,SAASV,SAASG,KAAK;IAC/C,MAAMiB,oBAAoBV,SAASV,SAASI,WAAW;IAEvD,MAAM,EAAEiB,SAASC,WAAW,EAAEC,UAAUC,YAAY,EAAE,GAAGC,IAAAA,kCAAgB,EACvER,YACA;QACE,6DAA6D;QAC7DS,YAAY;QACZ,yDAAyD;QACzDC,WAAWC,uBAAgB;IAC7B;IAGF,uCAAuC;IACvCC,IAAAA,mBAAY,EAACtB,SAASE,YAAYQ;IAClCa,IAAAA,sBAAe,EAACvB,SAASE,YAAYQ,YAAYc;IAEjDC,IAAAA,gBAAS,EAAC;QACR,IAAItB,SAAS,MAAM;YACjB,gCAAgC;YAChC,MAAMuB,KAAKC,WAAW;gBACpBlB,iBAAiB,CAAC;YACpB,GAAGY,uBAAgB;YACnB,OAAO,IAAMO,aAAaF;QAC5B;IACF,GAAG;QAACvB;KAAK;IAET,SAAS0B,OAAOC,KAAgC;YAuBnC9B;QAtBX,IAAI8B,UAAU,SAAS;YACrBH,WAAW;oBACG3B;gBAAZ,MAAM+B,OAAM/B,mBAAAA,QAAQgC,OAAO,qBAAfhC,iBAAiBiC,gBAAgB,CAAC;gBAC9C,IAAIF,KAAK;oBACP,MAAMG,aAAaH,GAAG,CAAC,EAAE,CAACI,YAAY,CAAC;oBACvCN,OAAOO,OAAOF;gBAChB;YACF;YACA;QACF;QAEA,IAAIJ,UAAU,QAAQ;YACpBH,WAAW;oBACG3B;gBAAZ,MAAM+B,OAAM/B,mBAAAA,QAAQgC,OAAO,qBAAfhC,iBAAiBiC,gBAAgB,CAAC;gBAC9C,IAAIF,KAAK;oBACP,MAAMM,YAAYN,IAAIO,MAAM,GAAG;oBAC/BT,OAAOQ;gBACT;YACF;YACA;QACF;QAEA,MAAME,MAAKvC,mBAAAA,QAAQgC,OAAO,qBAAfhC,iBAAiBwC,aAAa,CACvC,AAAC,kBAAeV,QAAM;QAGxB,IAAIS,IAAI;YACN9B,iBAAiBqB;YACjBS,sBAAAA,GAAIE,KAAK;QACX;IACF;IAEA,SAASC,cAAcC,CAAsC;QAC3DA,EAAEC,cAAc;QAEhB,OAAQD,EAAEE,GAAG;YACX,KAAK;gBACH,MAAMC,OAAOtC,gBAAgB;gBAC7BqB,OAAOiB;gBACP;YACF,KAAK;gBACH,MAAMC,OAAOvC,gBAAgB;gBAC7BqB,OAAOkB;gBACP;YACF,KAAK;gBACHlB,OAAO;gBACP;YACF,KAAK;gBACHA,OAAO;gBACP;YACF;gBACE;QACJ;IACF;IAEA,SAASmB;QACP5C,QAAQ;QACR,IAAIzB,aAAa,GAAG;YAClBV,sBAAsB;QACxB;IACF;IAEA,SAASgF;QACPhF,sBAAsB,CAAC8E,OAAS,CAACA;IACnC;IAEA,SAASG;QACP9C,QAAQ,CAAC+C;YACP,IAAIA,aAAa,MAAMtB,OAAO;YAC9B,OAAOpC,SAASC,IAAI;QACtB;IACF;IAEA,SAAS0D;QACP,IAAIjD,SAASV,SAASC,IAAI,EAAE;YAC1BU,QAAQ;QACV,OAAO;YACL8C;YACAvB,WAAW;gBACTE,OAAO;YACT;QACF;IACF;IAEA,SAASL;QACP,qCAAqC;QACrC,yCAAyC;QACzCpB,QAAQ,CAAC+C;YACP,IAAIA,aAAa1D,SAASC,IAAI,EAAE;gBAC9B,OAAO;YACT;YACA,OAAOyD;QACT;IACF;IAEA,SAASE;QACPjD,QAAQ;QACRtB;IACF;IAEA,MAAM,CAACwE,UAAUC,WAAW,GAAGlD,SAASmD,KAAK,CAAC,KAAK;IACnD,MAAMC,UAAU;QAAE,CAACH,SAAS,EAAE;QAAoB,CAACC,WAAW,EAAE;IAAE;IAElE,qBACE,sBAACG,YAAK;QACJC,mBAAiB;QACjBC,OACE;YACE,6BAA6B,AAAC,KAAEvC,uBAAgB,GAAC;YACjD,iCAAiCwC,iBAAU;YAC3CC,WAAW;YACXC,QAAQ;YACR,iDAAiD;YACjDC,QAAQ;YACRC,MAAM;YACN,CAACX,SAAS,EAAE;YACZ,CAACC,WAAW,EAAE;QAChB;;0BAIF,qBAACW,kBAAQ;gBACPC,KAAKjE;gBACLkE,iBAAc;gBACdC,iBAAe3D;gBACf4D,iBAAc;gBACdC,cAAY,AAAC,KAAE7D,CAAAA,aAAa,UAAU,MAAK,IAAE;gBAC7C8D,8BAA4B;gBAC5BnF,UAAUA;gBACVV,YAAYA;gBACZyE,gBAAgBA;gBAChBH,oBAAoBA;gBACpBwB,eAAeC,IAAAA,4BAAgB;gBAC/BC,gBAAgBC,IAAAA,qCAAiB;gBACjC5G,cAAcA;gBACd8B,OAAOA;;0BAIT,qBAAC+E,oBAAS;gBACRC,QAAQlE;gBACRmE,OAAO7B;gBACPhD,YAAYA;gBACZ0D,OAAOH;gBACPlF,YAAYA;gBACZyG,WAAWpG,gBAAgB,WAAW;;0BAIxC,qBAACqG,4BAAa;gBACZH,QAAQnE;gBACRoE,OAAO7B;gBACPhD,YAAYA;gBACZ0D,OAAOH;;0BAIT,qBAACyB,gCAAe;gBACdJ,QAAQjE;gBACRkE,OAAO7B;gBACPhD,YAAYA;gBACZ0D,OAAOH;gBACP3E,MAAMuE;gBACN/C,aAAaA;gBACbD,UAAUA;gBACVP,OAAOA;gBACPC,UAAUA;;YAIXgB,6BACC,qBAACoE;gBACChB,KAAKnE;gBACL0B,IAAG;gBACH0D,MAAK;gBACLC,KAAI;gBACJC,oBAAiB;gBACjBf,cAAW;gBACXgB,UAAU,CAAC;gBACXC,WAAU;gBACVC,WAAW/C;gBACXgD,iBAAezE;gBACf2C,OAAOH;0BAEP,cAAA,sBAAClE,QAAQoG,QAAQ;oBACfC,OAAO;wBACLpE;wBACAhB;wBACAC;oBACF;;sCAEA,sBAAC0E;4BAAIK,WAAU;;gCACZ7G,aAAa,mBACZ,qBAACkH;oCACCC,OAAO,AAAGnH,aAAW,MAAGA,CAAAA,eAAe,IAAI,UAAU,QAAO,IAAE;oCAC9DmD,OAAO;oCACPiE,OAAM;oCACNH,qBAAO,qBAACI;kDAAYrH;;oCACpBsH,SAASjD;;8CAGb,qBAAC6C;oCACCC,OAAO,AAAC,sBAAmBlH,CAAAA,gBAAgB,WAAW,SAAQ,IAAE;oCAChEmH,OAAM;oCACNjE,OAAO;oCACP8D,OAAOhH,gBAAgB,WAAW;oCAClCqH,SAAS,IAAM7F,QAAQX,SAASG,KAAK;oCACrCsG,0BAAwBtH,gBAAgB,WAAW;;gCAEpDK,4BACC,qBAAC4G;oCACCC,OAAM;oCACNC,OAAM;oCACNH,OAAM;mDAGR,qBAACC;oCACC/D,OAAO;oCACPgE,OAAM;oCACNC,OAAM;oCACNH,qBAAO,qBAACO;oCACRF,SAAS,IAAM7F,QAAQX,SAASE,KAAK;;;;sCAK3C,qBAACwF;4BAAIK,WAAU;sCACb,cAAA,qBAACK;gCACCO,kBAAgB;gCAChBL,OAAM;gCACNH,qBAAO,qBAACS,iBAAQ;gCAChBJ,SAAS,IAAM7F,QAAQX,SAASI,WAAW;gCAC3CiC,OAAO7C,cAAc,IAAI;;;;;;;;AAQzC;AAEA,SAASkH;IACP,qBACE,qBAACG;QACCC,OAAM;QACNC,OAAM;QACNC,QAAO;QACPC,SAAQ;QACRC,MAAK;kBAEL,cAAA,qBAACC;YACCD,MAAK;YACLE,UAAS;YACTC,UAAS;YACTC,GAAE;;;AAIV;AAEA,SAASlB,SAAS,KAcjB;IAdiB,IAAA,EAChB/D,KAAK,EACLiE,KAAK,EACLH,KAAK,EACLK,OAAO,EACPe,IAAI,EACJ,GAAG9I,OAQJ,GAdiB;IAehB,MAAM+I,gBACJ,OAAOhB,YAAY,cAAc,OAAOe,SAAS;IACnD,MAAM,EAAExF,SAAS,EAAEhB,aAAa,EAAEC,gBAAgB,EAAE,GAAGyG,IAAAA,iBAAU,EAAC3H;IAClE,MAAM4H,WAAW3G,kBAAkBsB;IAEnC,SAASsF;QACP,IAAIH,eAAe;YACjBhB,2BAAAA;YACAzE;YACA,IAAIwF,MAAM;gBACRK,OAAOlH,IAAI,CAAC6G,MAAM,UAAU;YAC9B;QACF;IACF;IAEA,qBACE,sBAAC7B;QACCK,WAAU;QACV8B,cAAYxF;QACZyF,iBAAeJ;QACflB,SAASmB;QACT,wDAAwD;QACxD,gCAAgC;QAChCI,aAAa;YACX,IAAIP,iBAAiBnF,UAAU2F,aAAajH,kBAAkBsB,OAAO;gBACnErB,iBAAiBqB;YACnB;QACF;QACA4F,cAAc,IAAMjH,iBAAiB,CAAC;QACtCgF,WAAW,CAAC9C;YACV,IAAIA,EAAEE,GAAG,KAAK,WAAWF,EAAEE,GAAG,KAAK,KAAK;gBACtCuE;YACF;QACF;QACAhC,MAAM6B,gBAAgB,aAAaQ;QACnClC,UAAU4B,WAAW,IAAI,CAAC;QACzB,GAAGjJ,KAAK;;0BAET,qBAACyJ;gBAAKnC,WAAU;0BAA6BO;;0BAC7C,qBAAC4B;gBAAKnC,WAAU;0BAA6BI;;;;AAGnD;AAEA,SAASI,WAAW,KAAkC;IAAlC,IAAA,EAAE4B,QAAQ,EAAwB,GAAlC;IAClB,qBACE,sBAACD;QACCnC,WAAU;QACVqC,mBAAiBD,WAAW;;0BAE5B,qBAACD;gBAAKnC,WAAU;;YACfoC;;;AAGP;AAIO,MAAMhK,6BAA8B"}