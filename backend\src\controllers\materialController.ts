import { Request, Response } from 'express';
import prisma from '../lib/prisma';
import { ApiResponse, CreateMaterialRequest, UpdateMaterialRequest } from '../types';
import { AppError, asyncHandler } from '../middleware/errorHandler';

// 获取所有物料（分页）
export const getMaterials = asyncHandler(async (req: Request, res: Response) => {
  const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc' } = req.query as any;
  
  const skip = (Number(page) - 1) * Number(limit);
  
  const [materials, total] = await Promise.all([
    prisma.material.findMany({
      skip,
      take: Number(limit),
      orderBy: {
        [sortBy]: sortOrder,
      },
      include: {
        materialPrices: {
          orderBy: {
            effectiveDate: 'desc',
          },
          take: 1, // 最新价格
        },
        _count: {
          select: {
            salesOrderDetails: true,
          },
        },
      },
    }),
    prisma.material.count(),
  ]);

  const response: ApiResponse = {
    success: true,
    message: 'Materials retrieved successfully',
    data: {
      materials,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit)),
      },
    },
  };

  res.json(response);
});

// 根据ID获取单个物料
export const getMaterialById = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  const material = await prisma.material.findUnique({
    where: { id },
    include: {
      materialPrices: {
        orderBy: {
          effectiveDate: 'desc',
        },
      },
      salesOrderDetails: {
        include: {
          salesOrder: {
            include: {
              customer: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 10, // 最近10个订单明细
      },
      _count: {
        select: {
          salesOrderDetails: true,
        },
      },
    },
  });

  if (!material) {
    throw new AppError('Material not found', 404);
  }

  const response: ApiResponse = {
    success: true,
    message: 'Material retrieved successfully',
    data: material,
  };

  res.json(response);
});

// 创建新物料
export const createMaterial = asyncHandler(async (req: Request, res: Response) => {
  const { materialName, baseUnit }: CreateMaterialRequest = req.body;

  const material = await prisma.material.create({
    data: {
      materialName,
      baseUnit,
    },
  });

  const response: ApiResponse = {
    success: true,
    message: 'Material created successfully',
    data: material,
  };

  res.status(201).json(response);
});

// 更新物料
export const updateMaterial = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const updateData: UpdateMaterialRequest = req.body;

  // 检查物料是否存在
  const existingMaterial = await prisma.material.findUnique({
    where: { id },
  });

  if (!existingMaterial) {
    throw new AppError('Material not found', 404);
  }

  const material = await prisma.material.update({
    where: { id },
    data: updateData,
  });

  const response: ApiResponse = {
    success: true,
    message: 'Material updated successfully',
    data: material,
  };

  res.json(response);
});

// 删除物料
export const deleteMaterial = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  // 检查物料是否存在
  const existingMaterial = await prisma.material.findUnique({
    where: { id },
    include: {
      _count: {
        select: {
          salesOrderDetails: true,
        },
      },
    },
  });

  if (!existingMaterial) {
    throw new AppError('Material not found', 404);
  }

  // 检查是否有关联的订单明细
  if (existingMaterial._count.salesOrderDetails > 0) {
    throw new AppError('Cannot delete material with existing order details', 400);
  }

  await prisma.material.delete({
    where: { id },
  });

  const response: ApiResponse = {
    success: true,
    message: 'Material deleted successfully',
  };

  res.json(response);
});

// 搜索物料
export const searchMaterials = asyncHandler(async (req: Request, res: Response) => {
  const { q, page = 1, limit = 10 } = req.query as any;

  if (!q) {
    throw new AppError('Search query is required', 400);
  }

  const skip = (Number(page) - 1) * Number(limit);

  const [materials, total] = await Promise.all([
    prisma.material.findMany({
      where: {
        materialName: {
          contains: q,
          mode: 'insensitive',
        },
      },
      skip,
      take: Number(limit),
      orderBy: {
        materialName: 'asc',
      },
      include: {
        materialPrices: {
          orderBy: {
            effectiveDate: 'desc',
          },
          take: 1,
        },
      },
    }),
    prisma.material.count({
      where: {
        materialName: {
          contains: q,
          mode: 'insensitive',
        },
      },
    }),
  ]);

  const response: ApiResponse = {
    success: true,
    message: 'Materials search completed',
    data: {
      materials,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit)),
      },
    },
  };

  res.json(response);
});
