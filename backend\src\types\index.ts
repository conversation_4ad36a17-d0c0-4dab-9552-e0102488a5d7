import { Request, Response } from 'express';

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
}

// 分页参数类型
export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 客户相关类型
export interface CreateCustomerRequest {
  customerName: string;
  contactPerson: string;
  address: string;
}

export interface UpdateCustomerRequest extends Partial<CreateCustomerRequest> {}

// 物料相关类型
export interface CreateMaterialRequest {
  materialName: string;
  baseUnit: string;
}

export interface UpdateMaterialRequest extends Partial<CreateMaterialRequest> {}

// 物料价格相关类型
export interface CreateMaterialPriceRequest {
  materialId: string;
  unitPrice: number;
  currency: string;
  effectiveDate?: Date;
}

export interface UpdateMaterialPriceRequest extends Partial<CreateMaterialPriceRequest> {}

// 订单相关类型
export interface CreateSalesOrderRequest {
  customerId: string;
  customerPoNumber?: string;
  orderDetails: CreateOrderDetailRequest[];
}

export interface CreateOrderDetailRequest {
  materialId: string;
  quantity: number;
  unit: string;
  unitPrice: number;
}

export interface UpdateSalesOrderRequest {
  customerId?: string;
  customerPoNumber?: string;
  orderStatus?: 'PENDING' | 'CONFIRMED' | 'PRODUCING' | 'SHIPPED' | 'COMPLETED' | 'CANCELLED';
}

// 扩展Express Request类型
export interface AuthenticatedRequest extends Request {
  user?: any; // 如果后续需要添加认证
}

// 控制器类型
export type ControllerFunction = (req: Request, res: Response) => Promise<void>;
